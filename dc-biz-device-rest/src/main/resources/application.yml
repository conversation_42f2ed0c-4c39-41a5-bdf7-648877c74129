spring:
  application:
    name: dc-biz-device-rest-dev
  config:
    import: "configserver:http://oam-test.iot.renwochong.com"
  profiles:
    active: test01,common,rabbitmq,jdbc-charger,redis,zipkin,mongodb
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    config:
      label: test01
  ribbon:
    ReadTimeout: 60000
    ConnectTimeout: 60000
  jackson:
    default-property-inclusion: non_null
  # 外网访问：本地测试用


management:
  endpoints:
    web:
      exposure:
        include: health,info,refresh

#超时时间10000毫秒 = 10秒(导出数据，需要时间较长，增加超时时间)
ribbon:
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 1

eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:<EMAIL>:7001/eureka/,http://aaa:<EMAIL>:7001/eureka/

feign:
  hystrix:
    enabled: true

hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 5000
          strategy: SEMAPHORE


server:
  port: 8891

logging:
  level:
    com.cdz360: 'DEBUG'
    com.chargerlinkcar: 'DEBUG'
    org.springframework: 'WARN'
    orr.springframework.data: 'DEBUG'
    org.springframework.cloud: 'WARN'
    org.springframework.cloud.config: 'INFO'
    org.springframework.cloud.netflix: 'INFO'
    feign: 'DEBUG'

  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} - %logger{36}.%M\\(%line\\) - %msg%n"

springdoc:
  packagesToScan: com.cdz360.biz.dc.rest
  swagger-ui:
    path: /swagger-ui.html

mybatis-plus:
#  configLocation: classpath:xml/mybatis-config.xml
  mapper-locations: classpath:com/cdz360/**/mapper/**/*Mapper.xml
  type-aliases-package: com.cdz360.biz.device.business.entity.po

ftp:
  host: ***************
  port: 21
  username: evseftp
  password: evseftp
  visit-path: ftp://${ftp.host}:${ftp.port}/
  nfs:
    path: D:\ftpnfs\