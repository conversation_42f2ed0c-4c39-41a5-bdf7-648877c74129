package com.cdz360.biz.device.business.constant;

import lombok.Getter;

/**
 * 站点状态枚举
 *
 * <AUTHOR>
 * @date Create on 2018/8/6 10:50
 */
@Getter
public enum SiteStatusEnum {
    /**
     * 已删除
     */
    STATUS_HAS_DELETE(0, "已删除"),
    /**
     * 待上线
     */
    STATUS_WAIT_ONLINE(1, "待上线"),
    /**
     * 已上线
     */
    STATUS_ONLINE(2, "已上线"),
    /**
     * 维护中
     */
    @Deprecated
    STATUS_MAINTAIN(4, "维护中")
    //
    ;

    /**
     * 状态码
     */
    private int value;
    /**
     * 描述
     */
    private String label;

    SiteStatusEnum(int value, String label) {
        this.value = value;
        this.label = label;
    }
}
