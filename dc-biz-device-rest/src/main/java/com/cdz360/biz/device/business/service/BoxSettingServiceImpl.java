package com.cdz360.biz.device.business.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.device.business.client.EvseCfgClient;
import com.cdz360.biz.device.business.client.IotDeviceMmgClient;
import com.cdz360.biz.device.business.client.MerchantFeignClient;
import com.cdz360.biz.device.business.client.UserFeignClient;
import com.cdz360.biz.device.business.constant.EvseCfgEnum;
import com.cdz360.biz.device.business.entity.param.ModifyEvseCfgParam;
import com.cdz360.biz.device.business.entity.po.BsBox;
import com.cdz360.biz.device.business.entity.po.BsBoxSetting;
import com.cdz360.biz.device.business.entity.po.BsCharger;
import com.cdz360.biz.device.business.entity.po.WhiteCardEvse;
import com.cdz360.biz.device.business.entity.po.WhiteCardEvseDto;
import com.cdz360.biz.device.business.entity.request.BoxListRequest;
import com.cdz360.biz.device.business.entity.request.BoxSettingListRequest;
import com.cdz360.biz.device.business.entity.request.BoxSettingResultRequest;
import com.cdz360.biz.device.business.entity.request.BoxSettingUpsertRequest;
import com.cdz360.biz.device.business.entity.request.SiteSimpleInfoRequest;
import com.cdz360.biz.device.business.entity.result.BoxInfoVo;
import com.cdz360.biz.device.business.entity.result.BoxSettingInfoVo;
import com.cdz360.biz.device.business.entity.result.BsBoxSettingBInfoVo;
import com.cdz360.biz.device.business.entity.result.BsBoxSettingVo;
import com.cdz360.biz.device.business.entity.result.ChargerBarCodeUrlVo;
import com.cdz360.biz.device.business.entity.result.EvsePasscodePo;
import com.cdz360.biz.device.business.entity.result.ListResponseEvseList;
import com.cdz360.biz.device.business.entity.result.OnlineBoxVo;
import com.cdz360.biz.device.business.entity.result.SiteDefultSettingVO;
import com.cdz360.biz.device.business.entity.result.SiteSimpleInfoVo;
import com.cdz360.biz.device.business.entity.result.UrgencyCardEvse;
import com.cdz360.biz.device.business.mapper.BsBoxQueryMapper;
import com.cdz360.biz.device.business.mapper.BsBoxSettingMapper;
import com.cdz360.biz.device.business.mapper.BsChargerQueryMapper;
import com.cdz360.biz.device.business.mapper.WhiteCardEvseMapper;
import com.cdz360.biz.device.business.util.DbsAssert;
import com.cdz360.biz.device.business.util.FeignResponseValidate;
import com.cdz360.biz.device.common.constant.DeviceStatusEnum;
import com.cdz360.biz.device.common.dingchong.CfgEvseParam;
import com.cdz360.biz.device.common.dingchong.CfgEvseV2;
import com.cdz360.biz.device.common.dingchong.ChargeStopMode;
import com.cdz360.biz.device.common.dingchong.WhiteCardV2;
import com.cdz360.data.cache.RedisIotReadService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * @Classname BoxSettingServiceImpl
 * @Description 桩配置服务实现
 * @Date 2019/6/6 16:59
 * @Created by JLei
 * @Email <EMAIL>
 */
@Slf4j
@Service
public class BoxSettingServiceImpl implements BoxSettingService {

    private static final Logger logger = LoggerFactory.getLogger(BoxQueryService.class);
    private static final int PAGE_SIZE = 500;
    @Autowired
    private BsBoxSettingMapper bsBoxSettingQueryMapper;
    @Autowired
    private BsChargerQueryMapper bsChargerQueryMapper;
    @Autowired
    private BsBoxQueryMapper bsBoxQueryMapper;
    @Autowired
    private MerchantFeignClient merchantFeignClient;
    @Autowired
    private EvseCfgClient evseCfgClient;
    @Autowired
    private BoxQueryService boxQueryService;
    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private WhiteCardEvseMapper whiteCardEvseMapper;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private IotDeviceMmgClient iotDeviceMmgClient;

    @Autowired
    private SiteQueryService siteQueryService;

    @Autowired
    private SiteDefultSettingService siteDefultSettingService;


    @Override
    public Map<String, Object> selectPagedBoxSettingList(BoxSettingListRequest request)
        throws DcServiceException {
        Page<BsBoxSettingVo> pageInfo = PageHelper.startPage(request.getPage(), request.getRows(),
            true);
        String orderString = "";
        if (StringUtils.isBlank(request.getOrderFiled())) {
            request.setOrderFiled("boxOutFactoryCode");
        }
        if (request.getIsAsc() == null || request.getIsAsc() == 1) {
            orderString = request.getOrderFiled() + " asc";
        } else {
            orderString = request.getOrderFiled() + " desc";
        }
        pageInfo.setOrderBy(orderString);
        List<BsBoxSettingBInfoVo> list = bsBoxSettingQueryMapper.selectBoxSettingList(request);

        if (CollectionUtils.isNotEmpty(list)) {
            ListResponse<com.cdz360.biz.device.business.entity.result.EvseVo> evseListRes = this.iotDeviceMmgClient.selectByEvseIds(
                list.stream().map(BsBoxSettingBInfoVo::getBoxOutFactoryCode)
                    .collect(Collectors.toList()));
            FeignResponseValidate.check(evseListRes);
            Map<String, com.cdz360.biz.device.business.entity.result.EvseVo> evseMap =
                evseListRes.getData().stream().collect(Collectors.toMap(
                    com.cdz360.biz.device.business.entity.result.EvseVo::getEvseId, o -> o));

            for (BsBoxSettingBInfoVo bsBoxSettingBInfoVo : list) {

                com.cdz360.biz.device.business.entity.result.EvseVo vo = evseMap.get(
                    bsBoxSettingBInfoVo.getBoxOutFactoryCode());
                if (vo != null) {
                    bsBoxSettingBInfoVo.setProtocolVer(vo.getProtocolVer())
                        .setPower(vo.getPower())
                        .setEvseStatus(
                            DeviceStatusEnum.convertFromEvseStatus(vo.getEvseStatus()).getCode());
                }

                getBarCodeUrlList(bsBoxSettingBInfoVo);
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("rows", list);
        resultMap.put("total", pageInfo.getTotal());
        return resultMap;
    }

    @Override
    public List<BsBoxSettingBInfoVo> selectAllBoxSettingList(BoxSettingListRequest request)
        throws DcServiceException {
        List<BsBoxSettingBInfoVo> list = bsBoxSettingQueryMapper.selectBoxSettingList(request);
        return list;
    }

    @Override
    public BsBoxSettingBInfoVo selectBoxSettingById(Long id) {
        BsBoxSettingBInfoVo bsBoxSettingBInfoVo = bsBoxSettingQueryMapper.selectBoxSettingById(id);
        if (bsBoxSettingBInfoVo != null) {
            getBarCodeUrlList(bsBoxSettingBInfoVo);
        }
        return bsBoxSettingBInfoVo;
    }

    @Override
    public Integer insertBoxSetting(BoxSettingUpsertRequest request) {
        BsBoxSetting bsBoxSetting = new BsBoxSetting();
        // 请求属性copy到bsBoxSetting
        BeanUtils.copyProperties(request, bsBoxSetting);
        bsBoxSetting.setCreateTime(new Date());
        bsBoxSetting.setUpdateTime(new Date());
//        fillTemplateCode(bsBoxSetting);
//        Integer res = bsBoxSettingQueryMapper.insert(bsBoxSetting);
        Integer res = bsBoxSettingQueryMapper.insertFillTemplateCode(bsBoxSetting);
        return res;
    }

    @Override
    public Integer updateBoxSetting(BoxSettingUpsertRequest request) {
        BsBoxSetting bsBoxSetting = bsBoxSettingQueryMapper.selectById(request.getId());
        DbsAssert.isNotNull(bsBoxSetting, "桩配置修改记录不存在");
        BeanUtils.copyProperties(request, bsBoxSetting);
        bsBoxSetting.setUpdateTime(new Date());
//        fillTemplateCode(bsBoxSetting);
//        Integer res = bsBoxSettingQueryMapper.updateById(bsBoxSetting);
        Integer res = bsBoxSettingQueryMapper.updateByIdFillTemplateCode(bsBoxSetting);
        return res;
    }


    @Override
    public Integer updateBoxSettingByEvseId(BoxSettingUpsertRequest request) {
        BsBox bsBox = bsBoxQueryMapper.selectOne(new QueryWrapper(new BsBox() {{
            setBoxOutFactoryCode(request.getBoxOutFactoryCode());
        }}));
        DbsAssert.isNotNull(bsBox, "该桩 " + request.getBoxOutFactoryCode() + " 记录不存在");
        BsBoxSetting bsBoxSetting = bsBoxSettingQueryMapper.selectOne(
            new QueryWrapper(new BsBoxSetting() {{
                setBoxOutFactoryCode(request.getBoxOutFactoryCode());
            }}));

        if (null != request.getWhiteCardsStatus() &&
            !StringUtils.isBlank(bsBoxSetting.getWhiteCardList()) &&
            request.getStatus() != null) {
            // 桩配置存在紧急充电卡，尝试更新卡状态，
            // 10007下发中，10008下发成功，10009下发失败
            // 下发中应该是开始时去设定的，所以这里只会设置为【成功】、【失败】
            log.info("更新紧急充电卡状态: {}, {}", bsBoxSetting.getWhiteCardList(),
                request.getStatus());
            QueryWrapper wapper = new QueryWrapper<WhiteCardEvse>();
            wapper.eq("evseId", request.getBoxOutFactoryCode());
            List<WhiteCardEvse> listWhiteCardEvse = whiteCardEvseMapper.selectList(wapper);
            log.info("listWhiteCardEvse: {}", JsonUtils.toJsonString(listWhiteCardEvse));
            // 弃用 : 6 ,格式 cardNo : 6
            String cardIds = listWhiteCardEvse
                .stream()
                .map(wc -> wc.getSendStatus() == 6 ?
                    wc.getCardNo().concat(":").concat(wc.getSendStatus().toString()) :
                    wc.getCardNo())
                .collect(Collectors.joining(","));
            ObjectResponse<Integer> res = userFeignClient.updateUrgencyCardStatus(
                request.getBoxOutFactoryCode(), cardIds, request.getStatus());
            log.info("更新紧急充电卡状态: 结果, {}", res);
        }

        DbsAssert.isNotNull(bsBoxSetting,
            "桩配置修改记录不存在,桩号:" + request.getBoxOutFactoryCode());
        Long id = bsBoxSetting.getId();
        BeanUtils.copyProperties(request, bsBoxSetting);
        bsBoxSetting.setUpdateTime(new Date());
        bsBoxSetting.setId(id);
//        fillTemplateCode(bsBoxSetting);
//        Integer res = bsBoxSettingQueryMapper.updateById(bsBoxSetting);
        Integer res = bsBoxSettingQueryMapper.updateByIdFillTemplateCode(bsBoxSetting);
        return res;
    }

    @Override
    public void sendBoxSetting(BoxSettingUpsertRequest request) {
        log.info("桩配置下发请求参数request：{}", request.toString());
        String boxOutFactoryCode = request.getBoxOutFactoryCode();
        BsBox bsBox = bsBoxQueryMapper.selectOne(new QueryWrapper(new BsBox() {{
            setBoxOutFactoryCode(boxOutFactoryCode);
        }}));
        DbsAssert.isNotNull(bsBox, "桩配置下发桩记录不存在");
        BsBoxSetting bsBoxSetting = bsBoxSettingQueryMapper.selectOne(
            new QueryWrapper(new BsBoxSetting() {{
                setBoxOutFactoryCode(boxOutFactoryCode);
            }}));

        EvseVo evseRedisCache = redisIotReadService.getEvseRedisCache(boxOutFactoryCode);
        List<EvseStatus> unSendEvseStatus = Arrays.asList(EvseStatus.OFFLINE, EvseStatus.OFF);

        DbsAssert.isTrue(evseRedisCache != null &&
            !unSendEvseStatus.contains(evseRedisCache.getStatus()), "桩离线或已下线");

        // 配置不存在插入数据库
        if (bsBoxSetting == null) {
            bsBoxSetting = new BsBoxSetting();
            BeanUtils.copyProperties(request, bsBoxSetting);
            bsBoxSetting.setBoxCode(bsBox.getBoxCode());
            log.info("桩配置不存在插入数据库");
//            fillTemplateCode(bsBoxSetting);
//            bsBoxSettingQueryMapper.insert(bsBoxSetting);
            bsBoxSettingQueryMapper.insertFillTemplateCode(bsBoxSetting);
        }
        // 更新或插入桩紧急卡配置中间表
        this.upsertWhiteCardEvseStatus(request, Arrays.asList(request.getBoxOutFactoryCode()));
        CfgEvseParam param = assembleCfgEvseParam(request);

        String evsePasscode = request.getEvsePasscode();
        // 更新长效秘钥
        if (StringUtils.isNotBlank(evsePasscode)) {
            ObjectResponse<Long> evsePasscodeRes = iotDeviceMmgClient.updateEvsePasscode(
                boxOutFactoryCode,
                evsePasscode);
            if (evsePasscodeRes == null ||
                evsePasscodeRes.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
                logger.warn("evsePasscodeRes = {}", evsePasscodeRes);
                throw new DcServiceException(evsePasscodeRes.getStatus(),
                    evsePasscodeRes.getError());
            }
            Long evsePasscodeVer = evsePasscodeRes.getData();
            if (evsePasscodeVer == null) {
                // 最新版本密钥未改变，设为空不做更改
                param.getCfgEvse().setEvsePasscode(null);
                param.getCfgEvse().setEvsePasscodeVer(null);
            } else {
                param.getCfgEvse().setEvsePasscode(evsePasscode);
                param.getCfgEvse().setEvsePasscodeVer(evsePasscodeVer.intValue());
            }
        }

        // 更新桩配置
        ModifyEvseCfgParam cfgParam = new ModifyEvseCfgParam();
        BeanUtils.copyProperties(request, cfgParam);
        cfgParam.setEvseNoList(Collections.singletonList(evseRedisCache.getEvseNo()));
        cfgParam.setQrUrl(request.getUrl());
        cfgParam.setEvsePasscode(param.getCfgEvse().getEvsePasscode());
        cfgParam.setEvsePasscodeVer(param.getCfgEvse().getEvsePasscodeVer());
        this.downSetting2Evse(cfgParam);
    }

    /**
     * 批量配置下发
     *
     * @param request
     */
    @Override
    public void sendBatchBoxSetting(BoxSettingUpsertRequest request) {
        log.info("批量配置下发。request: {}", request);

        List<String> evseIds = request.getBoxOutFactoryCodes();
        if (evseIds == null || evseIds.isEmpty()) {
            throw new DcArgumentException("桩配置批量下发桩号集合不能为空");
        }
        // 更新或插入桩紧急卡中间表
        this.upsertWhiteCardEvseStatus(request, evseIds);

        CfgEvseParam cfgEvseParam = assembleCfgEvseParam(request);
        log.info("批量配置下发。evseCfgClient cfgEvseParam: {}",
            JsonUtils.toJsonString(cfgEvseParam));
        BaseResponse rpcRes = evseCfgClient.modifyEvseCfg(JsonUtils.toJsonString(cfgEvseParam));

        if (rpcRes == null || rpcRes.getStatus() != 0) {
            this.updateBatchStatus(request, EvseCfgEnum.EVSE_CFG_SEND_FAIL.value);
            throw new DcServiceException(
                "桩配置批量配置下发失败。EvseIds = " + evseIds + "，原因：" + rpcRes.getError());
        }
        // 配置批量下发中
        this.updateBatchStatus(request, EvseCfgEnum.EVSE_CFG_SEND_PROCESS.value);
    }

    private void upsertWhiteCardEvseStatus(BoxSettingUpsertRequest request, List<String> evseIds) {
//        List<CfgEvseParam.CfgEvse.WhiteCard> whiteCards = request.getWhiteCards();
        List<WhiteCardV2> whiteCards = request.getWhiteCards();
        if (CollectionUtils.isNotEmpty(whiteCards)) {
            String whiteCardList = whiteCards.stream().map(wc -> wc.getCardNumber())
                .collect(Collectors.joining(","));
            this.upsertBatchWhiteCardEvse(request.getWhiteCardEvses(), evseIds);
            this.userFeignClient.updBatchCardStatusByNos(Arrays.asList(whiteCardList.split(",")),
                10007);
        }
    }

    @Override
    public Integer deleteBoxSetting(Long id) throws DcServiceException {
        BsBoxSetting bsBoxSetting = bsBoxSettingQueryMapper.selectById(id);
        DbsAssert.isNotNull(bsBoxSetting, "桩配置删除记录不存在");
        Integer res = bsBoxSettingQueryMapper.deleteById(id);
        return res;
    }


    @Override
    public Integer deleteBatchBoxSetting(List<Long> ids) throws DcServiceException {
        Integer res = bsBoxSettingQueryMapper.deleteBatchIds(ids);
        return res;
    }

    /**
     * 组装枪头二维码url列表
     *
     * @param bsBoxSettingBInfoVo
     * @throws DcServiceException
     */
    public void getBarCodeUrlList(BsBoxSettingBInfoVo bsBoxSettingBInfoVo)
        throws DcServiceException {
        List<BsCharger> bsChargerList = bsChargerQueryMapper.selectByMap(
            new HashMap<String, Object>() {{
                put("box_code", bsBoxSettingBInfoVo.getBoxCode());
            }});
        if (bsChargerList == null && bsChargerList.size() <= 0) {
            return;
        }

        String boxOutFactoryCode = bsBoxSettingBInfoVo.getBoxOutFactoryCode();
        List<ChargerBarCodeUrlVo> resultList = new ArrayList<>();
        for (BsCharger bs : bsChargerList) {
            String url = bsBoxSettingBInfoVo.getUrl();

            String evseNoAndConnectorId = boxOutFactoryCode.concat("0" + bs.getConnectorId());
            if (StringUtils.isNotBlank(url)) {
                if (!url.endsWith("/")) {
                    url = url.concat("/");
                }
                switch (bs.getCurrentType()) {
                    case 0:
                        url = url + "00" + evseNoAndConnectorId;
                        break;
                    case 1:
                        url = url + "01" + evseNoAndConnectorId;
                        break;
                    case 2:
                        url = url + "02" + evseNoAndConnectorId;
                        break;
                }
            }
            ChargerBarCodeUrlVo chargerInfoVo = new ChargerBarCodeUrlVo();
            chargerInfoVo.setChargerName(bs.getChargerName());
            chargerInfoVo.setQrCodeUrl(url);
            chargerInfoVo.setConnectorId(bs.getConnectorId());
            resultList.add(chargerInfoVo);
        }
        bsBoxSettingBInfoVo.setChargerList(resultList);
    }

    @Override
    public ListResponse<BsBoxSettingBInfoVo> getResultByCommIds(String commIdChain,
        BoxSettingResultRequest request) {
        Page<BsBoxSettingBInfoVo> pageInfo = PageHelper.startPage(request.getPage(),
            request.getRows(), true);
        List<BsBoxSettingBInfoVo> list = bsBoxSettingQueryMapper.selectResultByCommIds(commIdChain,
            request.getStatus());
        return new ListResponse<>(list, pageInfo.getTotal());
    }


    /**
     * 组装配置下发参数
     *
     * @param request
     */
    private CfgEvseParam assembleCfgEvseParam(BoxSettingUpsertRequest request) {
        log.info("组装配置下发参数。request = {}", request);
        CfgEvseParam cfgEvseParam = new CfgEvseParam();
        List<String> evseIds = request.getBoxOutFactoryCodes();
        String evseId = request.getBoxOutFactoryCode();
        if (evseIds != null && !evseIds.isEmpty()) {
            // 多个桩批量下发
            cfgEvseParam.setEvseIds(evseIds);
        } else if (StringUtils.isNotBlank(evseId)) {
            // 单个桩下发
            evseIds = new ArrayList<>();
            evseIds.add(evseId);
            cfgEvseParam.setEvseIds(evseIds);
        }
//        CfgEvseParam.CfgEvse cfgEvse = cfgEvseParam.getCfgEvse();
        CfgEvseV2 cfgEvse = cfgEvseParam.getCfgEvse();
        cfgEvse = Optional.ofNullable(cfgEvse).orElse(new CfgEvseV2());

        cfgEvse.setAdminCodeA(request.getAdminPassword());
        cfgEvse.setAdminCodeB(request.getLevel2Password());
        if (request.getIsVinCharge() != null) {
            cfgEvse.setVin(request.getIsVinCharge());
        }
        cfgEvse.setQrUrl(this.dualQrcodeUrl(request.getUrl()));
        cfgEvse.setDayVolume(request.getDayVolume());
        cfgEvse.setNightVolume(request.getNightVolume());

        if (request.getIsQueryChargeRecord() != null) {
            cfgEvse.setQueryChargeRecord(request.getIsQueryChargeRecord());
        }
        if (request.getIsScanCharge() != null) {
            cfgEvse.setQrCharge(request.getIsScanCharge());
        }
        if (request.getIsCardCharge() != null) {
            cfgEvse.setCardCharge(request.getIsCardCharge());
        }
        if (request.getIsNoCardCharge() != null) {
            cfgEvse.setNoCardCharge(request.getIsNoCardCharge());
        }
        if (request.getIsTimedCharge() != null) {
            cfgEvse.setTimedCharge(request.getIsTimedCharge());
        }

        ChargeStopMode stopMode = cfgEvse.getStopMode();
        if (stopMode == null) {
            stopMode = new ChargeStopMode();
        }
        if (request.getIsQuotaMoneyCharge() != null) {
            stopMode.setAmount(request.getIsQuotaMoneyCharge());
        }
        if (request.getIsQuotaEleCharge() != null) {
            stopMode.setKwh(request.getIsQuotaEleCharge());
        }
        if (request.getIsQuotaTimeCharge() != null) {
            stopMode.setTime(request.getIsQuotaTimeCharge());
        }
        cfgEvse.setStopMode(stopMode);
        if (request.getCharge() != null) {
            cfgEvse.setPrice(request.getCharge());
        }
        if (request.getWhiteCards() != null && request.getWhiteCards().size() > 0) {
            cfgEvse.setWhiteCards(request.getWhiteCards());
        }
        if (request.getChargeId() != null) {
            cfgEvse.setPriceCode(request.getChargeId().intValue());
        }
        cfgEvseParam.setCfgEvse(cfgEvse);
        log.info("组装配置下发参数。cfgEvseParam = {},cfgEvse ={}", cfgEvseParam.toString(),
            cfgEvse.toString());
        return cfgEvseParam;
    }

    /**
     * 配置下发批量更新
     *
     * @param request
     * @param status
     */
    public void updateBatchStatus(BoxSettingUpsertRequest request, Integer status) {
        log.info("配置下发批量更新。request: {}, status: {}", request, status);
        List<String> evseIds = request.getBoxOutFactoryCodes();
        List<WhiteCardV2> whiteCards = request.getWhiteCards();

        String whiteCardList = "";
        if (CollectionUtils.isNotEmpty(whiteCards)) {
            whiteCardList = whiteCards.stream().map(WhiteCardV2::getCardNumber)
                .collect(Collectors.joining(","));
        }

        // 获取桩配置对应BC_CODE
        Map<String, String> bsBoxMap = new HashMap<>();
        if (status == EvseCfgEnum.EVSE_CFG_SEND_PROCESS.value) {
            QueryWrapper<BsBox> bsboxWrapper = new QueryWrapper<>();
            bsboxWrapper.in("box_out_factory_code", evseIds);
            List<BsBox> bsBoxList = bsBoxQueryMapper.selectList(bsboxWrapper);
            if (CollectionUtils.isNotEmpty(bsBoxList)) {
                bsBoxMap = bsBoxList.stream()
                    .collect(Collectors.toMap(BsBox::getBoxOutFactoryCode, BsBox::getBoxCode));
            }
        }

        QueryWrapper<BsBoxSetting> entityWrapper = new QueryWrapper<>();
        entityWrapper.in("boxOutFactoryCode", evseIds);
        List<BsBoxSetting> list = bsBoxSettingQueryMapper.selectList(entityWrapper);

        if (CollectionUtils.isNotEmpty(list)) {
            for (BsBoxSetting bsBoxSetting : list) {
                if (status == EvseCfgEnum.EVSE_CFG_SEND_PROCESS.value) {
                    Long id = bsBoxSetting.getId();
                    String bcCode = bsBoxMap.get(bsBoxSetting.getBoxOutFactoryCode());
                    BeanUtils.copyProperties(request, bsBoxSetting);
                    bsBoxSetting.setId(id);
                    bsBoxSetting.setBoxCode(bcCode);
                    if (StringUtils.isNotBlank(whiteCardList)) {
                        bsBoxSetting.setWhiteCardList(whiteCardList);
                    }
                }
                bsBoxSetting.setUpdateTime(new Date());
                bsBoxSetting.setStatus(status);
//                fillTemplateCode(bsBoxSetting);
//                bsBoxSettingQueryMapper.updateById(bsBoxSetting);
                bsBoxSettingQueryMapper.updateByIdFillTemplateCode(bsBoxSetting);
            }
            //bsBoxSettingQueryMapper.updateBatchById(list);
        } else {
            log.warn("配置下发批量更新, 没有查询到信息, request:{}",
                JsonUtils.toJsonString(request));
        }
    }

    /**
     * 配置下发状态更新
     *
     * @param request
     * @param bsBoxSetting
     */
    public int updateStatus(BoxSettingUpsertRequest request, BsBoxSetting bsBoxSetting) {
        log.info("配置下发更新。request: {}, status: {}", request, bsBoxSetting.getStatus());
        Integer status = bsBoxSetting.getStatus();
        if (status == EvseCfgEnum.EVSE_CFG_SEND_PROCESS.value) {
            request.setId(bsBoxSetting.getId());
            request.setBoxCode(bsBoxSetting.getBoxCode());
            BeanUtils.copyProperties(request, bsBoxSetting);

//            List<CfgEvseParam.CfgEvse.WhiteCard> whiteCards = request.getWhiteCards();
            List<WhiteCardV2> whiteCards = request.getWhiteCards();
            if (CollectionUtils.isNotEmpty(whiteCards)) {
                String whiteCardList = whiteCards.stream().map(wc -> wc.getCardNumber())
                    .collect(Collectors.joining(","));
                bsBoxSetting.setWhiteCardList(whiteCardList);
            }
        }
        bsBoxSetting.setUpdateTime(new Date());
        bsBoxSetting.setStatus(status);
//        fillTemplateCode(bsBoxSetting);
//        return bsBoxSettingQueryMapper.updateById(bsBoxSetting);
        return bsBoxSettingQueryMapper.updateByIdFillTemplateCode(bsBoxSetting);
    }

    /**
     * 根据token找（子）商户的桩，随之做二维码配置下发，成功返回下发个数
     *
     * @param
     * @param qrCode
     * @return
     */
    @Override
    public Integer updateBoxSettingQRCode(List<Long> ids, String qrCode) {
        DbsAssert.isTrue(ids != null && !ids.isEmpty(), "没有找到任何商户");
        BoxListRequest query = new BoxListRequest();
        query.setCommercialIds(ids);
        query.setRows(PAGE_SIZE);
        query.setPage(1);//TODO 这里最好使用常量去表示
        //query.setIsUseSiteDefaultSetting(1);//下发所有桩

        ListResponse<BoxSettingInfoVo> boxes = boxQueryService.getPagedBoxSettingSimpleList(query);

        int count = 0;

        //TODO 需要分多次事务来提交
        while (count < boxes.getTotal()) {

            boxes = boxQueryService.getPagedBoxSettingSimpleList(query);
            List<String> evseIds = boxes.getData().stream()
                .map(e -> String.valueOf(e.getSerialNumber())).collect(Collectors.toList());

            BoxSettingUpsertRequest boxSettingUpsertRequest = new BoxSettingUpsertRequest();
            boxSettingUpsertRequest.setBoxOutFactoryCodes(evseIds);
            boxSettingUpsertRequest.setUrl(qrCode);

            this.sendBatchBoxSetting(boxSettingUpsertRequest);
            //            this.updateBatchStatus(boxSettingUpsertRequest, 3);

            query.setPage(query.getPage() + 1);
            count += boxes.getData().size();
        }

        //        CfgEvseParam cfgEvseParam = new CfgEvseParam();
        //        BaseRpcResponse res = evseCfgClient.modifyEvseCfg(cfgEvseParam);
        return count;
    }

    /**
     * 根据桩配置id，提取二维码url，之后发送
     *
     * @param token
     * @param id
     * @return
     */
    // 注: 运营支撑平台中修改商户的二维码
    @Override
    public Integer sendQRCodeById(String token, Long id) {
        BsBoxSettingBInfoVo bsBoxSettingBInfoVo = bsBoxSettingQueryMapper.selectBoxSettingById(id);
        DbsAssert.isTrue(bsBoxSettingBInfoVo != null, "没找到对应的装配置");
        //TODO 此处需要检查桩是否属于token对应的商户
        BoxSettingUpsertRequest boxSettingUpsertRequest = new BoxSettingUpsertRequest();
        boxSettingUpsertRequest.setBoxOutFactoryCode(bsBoxSettingBInfoVo.getBoxOutFactoryCode());
        boxSettingUpsertRequest.setUrl(this.dualQrcodeUrl(bsBoxSettingBInfoVo.getUrl()));
        this.sendBoxSetting(boxSettingUpsertRequest);
        return 1;
    }

    /**
     * 根据token和商户id获得（子）商户下发失败的桩后，对这些桩进行二维码再下发
     *
     * @param token
     * @return
     */
    @Override
    public Integer sendFailQRCodeByToken(String token, Long commId) {

        log.info("二维码再次下发。token: {}, commId: {}", token, commId);

        List<Long> commId4Query = new ArrayList<>();
        commId4Query.add(commId);

        ListResponse<BsBoxSettingBInfoVo> paginationEntity = getFailQRCodeByCommId(commId4Query, 1,
            PAGE_SIZE);

        //TODO 获取第一个查到的二维码url,此处需要再优化
        String url = null;
        if (paginationEntity.getTotal() > 0) {
            url = paginationEntity.getData().get(0).getUrl();
        }

        log.info("二维码再次下发。url: {}", url);

        int count = 0;
        int pageNum = 1;

        //TODO 需要分多次事务来提交
        while (count < paginationEntity.getTotal()) {

            paginationEntity = getFailQRCodeByCommId(commId4Query, pageNum, PAGE_SIZE);

            log.info("获取商户下发失败的桩配置。commIds: {}, pageNum: {}, pageSize: {}, result: {}",
                commId4Query, pageNum, PAGE_SIZE, paginationEntity);

            List<String> evseIds = paginationEntity.getData().stream()
                .map(e -> String.valueOf(e.getBoxOutFactoryCode())).collect(Collectors.toList());

            BoxSettingUpsertRequest boxSettingUpsertRequest = new BoxSettingUpsertRequest();
            boxSettingUpsertRequest.setBoxOutFactoryCodes(evseIds);
            boxSettingUpsertRequest.setUrl(this.dualQrcodeUrl(url));

            this.sendBatchBoxSetting(boxSettingUpsertRequest);
            //this.updateBatchStatus(boxSettingUpsertRequest, 3);

            pageNum += 1;
            count += paginationEntity.getData().size();
        }
        return count;
    }

    /**
     * 多场站紧急充电卡批量下发
     *
     * @param requestList
     */
    @Override
    public Map<String, Object> sendBatchWhiteCards(List<BoxSettingListRequest> requestList) {
        log.info(">> 多场站下发紧急充电卡开始。req size = {}", requestList.size());
        Map<String, Object> resMap = new HashMap<>();

        // 并行处理紧急卡下发
        requestList.parallelStream().forEach(req -> {
            List<String> strings = this.sendWhiteCard(req);
            if (CollectionUtils.isNotEmpty(strings)) {
                resMap.put(req.getSiteId(), strings);
            }
        });

        log.info("<< 多场站下发紧急充电卡结束。");
        return resMap;
    }

    /**
     * 更新紧急充电卡-桩关系表
     *
     * @param evseId
     * @param status 2-成功，1-失败
     * @return
     */
    @Override
    public int updateStatusOnEvseIdAndStatus(String evseId, int status) {
        log.info("更新紧急充电卡-桩关系表evseId: {}, status: {}", evseId, status);
        WhiteCardEvseDto param = new WhiteCardEvseDto();
        param.setEvseId(evseId);
        List<WhiteCardEvse> before = whiteCardEvseMapper.queryByCondition(param);
        for (WhiteCardEvse tmp : before) {
            log.info("befor：{},{}", tmp.getWhiteCardNo(), tmp.getSendStatus());
        }
        return whiteCardEvseMapper.updateStatusOnEvseIdAndStatus(evseId, status);
    }

    /**
     * 单场站紧急充电卡批量下发
     *
     * @param request
     */
    @Override
    public List<String> sendWhiteCard(BoxSettingListRequest request) {
        log.info(">> 单场站下发紧急充电卡开始。request = {}", request);
        // 查询站点下在线桩列表
        String siteId = request.getSiteId();
        BoxListRequest boxListRequest = new BoxListRequest();
        boxListRequest.setSiteId(siteId);
        if (StringUtils.isNotBlank(request.getSingleEvseNo())) {
            boxListRequest.setDeviceIdList(List.of(request.getSingleEvseNo())); // 若有值，则认为单桩下发紧急卡
        }
        OnlineBoxVo onlineBoxVo = boxQueryService.getOnlineBoxSimpleList(boxListRequest);
        List<BoxInfoVo> onlineBoxList = onlineBoxVo.getOnlineBoxInfoVos();

        // 查询场站
        SiteSimpleInfoRequest req = new SiteSimpleInfoRequest();
        req.setSiteId(siteId);
        SiteSimpleInfoVo site = siteQueryService.getSiteSimpleInfoById(req);
        if (null == site) {
            log.info("查无该场站 siteId={}", siteId);
//            throw new DcArgumentException("查无该场站");
            return new ArrayList<>();
        }

        log.info("单站点下发紧急充电卡。站点SiteId = {}，在线桩列表onlineBoxList ={}", siteId,
            onlineBoxList);
        if (onlineBoxList == null || onlineBoxList.size() <= 0) {
//            throw new DcServiceException("未下发，场站[" + site.getSiteName() + "}下无在线充电桩");
            log.info("未下发，场站[" + site.getSiteName() + "}下无在线充电桩");
            return new ArrayList<>();
        }

        // 离线/在线区分
        // FIXME: 离线的桩要更新状态？
        // 离线桩变更状态: 下发失败
        BoxSettingUpsertRequest updateStatus = new BoxSettingUpsertRequest();
        updateStatus.setWhiteCards(request.getWhiteCardList());
        updateStatus.setWhiteCardEvses(request.getWhiteCardEvses());
        if (CollectionUtils.isNotEmpty(onlineBoxVo.getOfflineEvseIds())) {
            log.info("离线桩下发状态更新");
            updateStatus.setBoxOutFactoryCodes(onlineBoxVo.getOfflineEvseIds());
            this.updateBatchStatus(updateStatus, EvseCfgEnum.EVSE_CFG_SEND_FAIL.value);
        }

        // 在线桩Id列表
        List<String> evseIds = onlineBoxList.stream().map(BoxInfoVo::getSerialNumber)
            .collect(Collectors.toList());

        // 更新或插入桩紧急卡中间表
        if (CollectionUtils.isNotEmpty(request.getWhiteCardList())) {
            List<String> whiteCardNumList = request.getWhiteCardList().stream()
                .map(WhiteCardV2::getCardNumber)
                .collect(Collectors.toList());
            this.upsertBatchWhiteCardEvse(request.getWhiteCardEvses(), evseIds);
            this.userFeignClient.updBatchCardStatusByNos(whiteCardNumList, 10007);
            log.info("更新完在线桩与紧急卡的关联记录状态");
        }

        // 紧急卡下发
        ModifyEvseCfgParam param = new ModifyEvseCfgParam();
        param.setEvseNoList(evseIds);
        param.setWhiteCards(request.getWhiteCardList());
        BaseResponse baseResponse = evseCfgClient.modifyEvseCfgV2(param);
        log.info("紧急卡下发结果: result = {}", baseResponse);

        // 下发状态更新
        updateStatus.setBoxOutFactoryCodes(evseIds);
        if (baseResponse == null || baseResponse.getStatus() != 0) {
            this.updateBatchStatus(updateStatus, EvseCfgEnum.EVSE_CFG_SEND_FAIL.value);
            throw new DcServiceException("紧急卡下发失败");
        }

        // 配置批量下发中
        this.updateBatchStatus(updateStatus, EvseCfgEnum.EVSE_CFG_SEND_PROCESS.value);

        log.info(">> 单场站下发紧急充电卡结束。");
        return onlineBoxVo.getOfflineEvseIds();
    }

    /**
     * 获取长效密钥
     *
     * @param evseNo
     */
    @Override
    public ObjectResponse<EvsePasscodePo> getEvsePasscode(String evseNo) {
        log.info(">> 获取桩长效密钥。evseNo = {}", evseNo);
        ObjectResponse<EvsePasscodePo> evsePasscodeRes = iotDeviceMmgClient.getEvsePasscode(evseNo,
            null);
        log.info(">> 获取桩长效密钥结束。EvsePasscodePo = {}", evsePasscodeRes.getData());
        return evsePasscodeRes;
    }

    /**
     * 获取（子）商户下发失败的桩配置
     *
     * @param commIds
     * @param pageNum
     * @param pageSize
     * @return
     */
    private ListResponse<BsBoxSettingBInfoVo> getFailQRCodeByCommId(List<Long> commIds, int pageNum,
        int pageSize) {
        log.info("获取商户下发失败的桩配置。commIds: {}, pageNum: {}, pageSize: {}", commIds,
            pageNum, pageSize);
        Page<BsBoxSettingBInfoVo> pageInfo = PageHelper.startPage(pageNum, pageSize, true);
        List<BsBoxSettingBInfoVo> list = bsBoxSettingQueryMapper.getPagedBoxSettingFailList(
            commIds);
        return new ListResponse<>(list, pageInfo.getTotal());
    }

    /**
     * 根据下发状态进行Count
     *
     * @param stationCode     必传，站点编号
     * @param whiteCardStatus 可传，紧急充电卡下发状态1下发成功2失败3下发中
     * @return
     */
    @Override
    public Integer selectCountByStationCodeAndWhiteCardStatus(String stationCode,
        Integer whiteCardStatus) {
        return bsBoxSettingQueryMapper.selectCountByStationCodeAndWhiteCardStatus(stationCode,
            whiteCardStatus);
    }

    /**
     * 根据stationIds获取相关桩的信息，[,]分割
     *
     * @param stationIds
     */
    @Override
    public ListResponseEvseList<UrgencyCardEvse> urgencyCardsDetailEvseList(String stationIds,
        String cardChipNo, String evse, int page, int pageSize) {
        log.info(
            "根据站点id查找关联的桩及其配置信息:stationIds = {},cardChipNo = {},evse = {},page = {},pageSize = {}",
            stationIds, cardChipNo, evse, page, pageSize);
        Assert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(stationIds), "场站id不能为空");

        BoxSettingListRequest boxSettingListRequest = new BoxSettingListRequest();

        List<String> stationIdList = Arrays.asList(stationIds.split(","));

        BoxListRequest boxListRequest = new BoxListRequest();
        boxListRequest.setSiteId(stationIdList.get(0));
        OnlineBoxVo onlineBoxVo = boxQueryService.getOnlineBoxSimpleList(boxListRequest);

        boxSettingListRequest.setSiteIds(stationIdList);
        boxSettingListRequest.setCardChipNo(cardChipNo);
        boxSettingListRequest.setEvse(evse);
        Page<BsBoxSettingBInfoVo> pageInfo = PageHelper.startPage(page, pageSize, true);
        List<BsBoxSettingBInfoVo> list = bsBoxSettingQueryMapper.selectBoxSettingListForUrgencyCard(
            boxSettingListRequest);

        List retList = list.stream().map(e -> {
            UrgencyCardEvse urgencyCardEvse = new UrgencyCardEvse();
            urgencyCardEvse.setEvseNo(e.getBoxOutFactoryCode());
            urgencyCardEvse.setEvseName(e.getBoxName());
            EvseVo cache = redisIotReadService.getEvseRedisCache(e.getBoxOutFactoryCode());
            if (cache == null) {
                urgencyCardEvse.setStatus(DeviceStatusEnum.STATUS_OFFLINE.getCode());
            } else {
                DeviceStatusEnum status = DeviceStatusEnum.convertFromEvseStatus(cache.getStatus());
                if (status == DeviceStatusEnum.STATUS_ONLINE) {
                    urgencyCardEvse.setStatus(DeviceStatusEnum.STATUS_ONLINE.getCode());
                } else {
                    urgencyCardEvse.setStatus(DeviceStatusEnum.STATUS_OFFLINE.getCode());
                }
            }
            urgencyCardEvse.setWhiteCardCfgStatus(e.getSendStatus());
            urgencyCardEvse.setPassword(e.getUrgencyPassword());
            return urgencyCardEvse;
        }).collect(Collectors.toList());

        ListResponseEvseList<UrgencyCardEvse> ret = new ListResponseEvseList(retList,
            pageInfo.getTotal());
        ret.setOnlineCount(onlineBoxVo.getOnlineBoxInfoVos().size());
        ret.setOfflineCount(onlineBoxVo.getOfflineEvseIds().size());

        return ret;
    }

    /**
     * 批量修改订单状态 先Upser插入中间表在下发配置到桩
     *
     * @param whiteCardEvses
     * @param evseIds
     */
    public void upsertBatchWhiteCardEvse(List<WhiteCardEvse> whiteCardEvses, List<String> evseIds) {
        List<WhiteCardEvse> whiteCardEvselist = new ArrayList<>();
        // 场站下所有桩和紧急充电卡关联关系
        evseIds.forEach(evseId -> {
            whiteCardEvses.forEach(wce -> {
                WhiteCardEvse whiteCardEvse = new WhiteCardEvse();
                BeanUtils.copyProperties(wce, whiteCardEvse);
                whiteCardEvse.setEvseId(evseId);
                whiteCardEvselist.add(whiteCardEvse);
            });
        });
        // 桩下卡片下发弃用记录
        List<WhiteCardEvse> insertList = new ArrayList<>();
        whiteCardEvselist.forEach(wce -> {
            int count = whiteCardEvseMapper.selectByEvseAndCardNo(wce.getEvseId(),
                wce.getWhiteCardNo());
            if (count == 0) {
                insertList.add(wce);
            } else {
                whiteCardEvseMapper.updateByEvseIdAndCardNo(wce);
            }
        });
        if (!insertList.isEmpty()) {
            whiteCardEvseMapper.batchInsert(insertList);
        }
    }

    private String dualQrcodeUrl(String qrcodeUrl) {
        try {
            log.info("二维码地址。qrcodeUrl ={}", qrcodeUrl);
            if (StringUtils.isBlank(qrcodeUrl)) {
                return qrcodeUrl;
            }
//            if(!qrcodeUrl.endsWith("/")){
//                qrcodeUrl = qrcodeUrl.concat("/");
//            }
            String ret = new URL(qrcodeUrl).toString();
            ret = org.apache.commons.lang3.StringUtils.stripEnd(ret, "/");
            return ret.concat("/");
        } catch (MalformedURLException e) {
            throw new DcServiceException("二维码地址配置不规范");
        }
    }


    /**
     * 给桩下发场站默认配置信息
     *
     * @param evseNo 桩编号
     * @param siteId 场站Id
     * @return
     */
    @Override
    public void downSetting2Evse(String evseNo, String siteId) {
        log.info("给桩下发场站默认配置: evseNo = {}, siteId = {}", evseNo, siteId);
        if (StringUtils.isBlank(evseNo) || StringUtils.isBlank(siteId)) {
            log.info("桩编号或场站Id没有提供");
            throw new DcArgumentException("桩编号或场站Id没有提供");
        }

        // t_site_defult_setting
        SiteDefultSettingVO siteDefaultSetting = siteDefultSettingService.selectBySiteId(siteId);
        if (null == siteDefaultSetting) {
            log.info("该场站没有默认配置信息");
            throw new DcArgumentException("场站Id无效, 没有默认配置信息");
        }

        // 配置下发
        ModifyEvseCfgParam param = new ModifyEvseCfgParam();
        BeanUtils.copyProperties(siteDefaultSetting, param);
        param.setQrUrl(siteDefaultSetting.getUrl());
        param.setEvseNoList(Collections.singletonList(evseNo));
        this.downSetting2Evse(param);
    }

    /**
     * 给桩下发场站默认配置信息
     *
     * @param param
     */
    @Override
    public void downSetting2Evse(ModifyEvseCfgParam param) {
        log.info("给桩下发场站默认配置: param = {}", JsonUtils.toJsonString(param));
        if (CollectionUtils.isEmpty(param.getEvseNoList())) {
            log.info("桩编号或场站Id没有提供");
            throw new DcArgumentException("桩编号或场站Id没有提供");
        }

        // t_bs_box_setting 中记录是否存在
        param.getEvseNoList().forEach(evseNo -> this.addBoxSetting(evseNo, param));

        // 变更桩配置状态
        this.updateBoxSettingStatus(param, EvseCfgEnum.EVSE_CFG_SEND_PROCESS);

        // 配置下发
        BaseResponse baseResponse = evseCfgClient.modifyEvseCfgV2(param);
        log.info("下发桩关联的场站配置信息完成");

        // 变更桩配置状态
        if (null == baseResponse || baseResponse.getStatus() != 0) {
            log.warn("下发配置iot返回异常");
            this.updateBoxSettingStatus(param, EvseCfgEnum.EVSE_CFG_SEND_FAIL);
        }
    }

    private void addBoxSetting(String evseNo, ModifyEvseCfgParam param) {
        log.info("保证桩配置记录可查询，不然上报变更时不可能查询报错: evseNo = {}", evseNo);
        BsBoxSetting setting = new BsBoxSetting();
        setting.setBoxOutFactoryCode(evseNo);
        BsBoxSetting bsBoxSetting = bsBoxSettingQueryMapper.selectOne(
            new QueryWrapper<BsBoxSetting>(setting));

        // 配置不存在插入数据库
        log.info("桩配置记录是否存在(true-表存在): result = {}", bsBoxSetting != null);
        if (bsBoxSetting == null) {
            bsBoxSetting = new BsBoxSetting();
            BeanUtils.copyProperties(param, bsBoxSetting);
            bsBoxSetting.setUrl(param.getQrUrl());
            bsBoxSetting.setBoxOutFactoryCode(evseNo);
            bsBoxSetting.setBoxCode(evseNo);
            bsBoxSetting.setTemplateCode(null); // 计费模板信息没有
            bsBoxSetting.setChargeId(null); // 计费模板为空
            bsBoxSetting.setWhiteCardList(null); // 紧急充电卡为空
            bsBoxSetting.setCreateTime(new Date());
            bsBoxSetting.setUpdateTime(new Date());

            bsBoxSettingQueryMapper.insertFillTemplateCode(bsBoxSetting);
            log.info("新增桩配置记录: evseNo = {}", evseNo);
        } else {
            // 存在则更新
            BeanUtils.copyProperties(param, bsBoxSetting);
            bsBoxSetting.setUrl(param.getQrUrl());
            bsBoxSetting.setUpdateTime(new Date());
            bsBoxSettingQueryMapper.updateByIdFillTemplateCode(bsBoxSetting);
        }
    }

    private void updateBoxSettingStatus(ModifyEvseCfgParam param, EvseCfgEnum status) {
        log.info("更新桩配置: status = {}, size = {}", status, param.getEvseNoList().size());
        QueryWrapper<BsBoxSetting> entityWrapper = new QueryWrapper<>();
        entityWrapper.in("boxOutFactoryCode", param.getEvseNoList());
        List<BsBoxSetting> list = bsBoxSettingQueryMapper.selectList(entityWrapper);
        log.info("查询记录 size = {}", list.size());

        if (CollectionUtils.isNotEmpty(list)) {
            for (BsBoxSetting bsBoxSetting : list) {
                bsBoxSetting.setUpdateTime(new Date());
                bsBoxSetting.setStatus(status.value);
                bsBoxSettingQueryMapper.updateByIdFillTemplateCode(bsBoxSetting);
            }
        }
    }
}
