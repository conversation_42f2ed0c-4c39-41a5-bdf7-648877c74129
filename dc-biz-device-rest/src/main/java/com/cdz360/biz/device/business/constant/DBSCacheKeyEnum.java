package com.cdz360.biz.device.business.constant;


import com.cdz360.biz.device.business.entity.result.BoxInfoVo;
import com.cdz360.biz.device.business.entity.result.SiteDetailInfoVo;
import com.cdz360.biz.device.common.cache.ICacheKey;

/**
 * <AUTHOR>
 * @date Create on 2018/8/22 21:05
 */
public enum DBSCacheKeyEnum implements ICacheKey {
    /**
     * 设备ID——站点ID关联关系，set存储，key为设备ID，value为站点ID
     */
    DEVICE_ID_REL_STATION_ID_6("dm:device_id_rel_station_id:"),
    /**
     * 站点详情，set存储，key为站点ID，value为{@link SiteDetailInfoVo}
     */
    STATION_DETAIL("dm:station_detail:", 10 * 60L),
    /**
     * 站点计费模板信息，set存储，key为计费模板ID，value为{@link com.chargerlink.device.business.entity.result.BillTemplateVo}
     */
    BILL_TEMPLATE_INFO("dm:bill_template_info:", 10 * 60L),

    /**
     * 商户绑定计费模板信息 key:商户ID value 计费模板code
     */
    MERCHANT_TEMPLATE_INFO("dm:merchant_template_info:"),
    /**
     * 批量下发计费模板存储需要下发模板的设备总数
     */
    SEND_TEMPLATE("dm:send_template:"),
    /**
     * 批量下发计费成功缓存，hash存储，key为计费模板ID，value为{@link BoxInfoVo}
     */
    SEND_TEMPLATE_SUCCESS("dm:send_template_success:"),
    /**
     * 批量下发计费失败缓存，hash存储，key为计费模板ID，value为{@link BoxInfoVo}
     */
    SEND_TEMPLATE_FAIL("dm:send_template_fail:"),
    /**
     * 站点计费模板信息，set存储，key为站点ID，value为{@link com.chargerlink.device.business.entity.result.BillTemplateVo}
     */
    BILL_TEMPLATE_INFO_REL_SITE_ID("dm:bill_template_info_rel_site_id:", 10 * 60L),

    EVSE_BUNDLE_PROGRESS("evse_bundle_progress")
    //
    ;
    /**
     * key值前缀
     */
    private String keyPrefix;
    /**
     * 超时时间
     */
    private Long timeout;

    DBSCacheKeyEnum(String keyPrefix) {
        this.keyPrefix = keyPrefix;
    }

    DBSCacheKeyEnum(String keyPrefix, Long timeout) {
        this.keyPrefix = keyPrefix;
        this.timeout = timeout;
    }


    @Override
    public Long getTimeout() {
        return timeout;
    }

    @Override
    public String getKeyPrefix() {
        return keyPrefix;
    }

}
