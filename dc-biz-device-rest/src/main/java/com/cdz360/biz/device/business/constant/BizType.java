package com.cdz360.biz.device.business.constant;

import com.cdz360.base.model.base.type.DcEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

/**
 * @Classname BizType
 * @Description TODO
 * @Date 2019/11/26 8:44
 * @Created by song<PERSON><PERSON>
 */
@ApiModel(value = "运营属性")
@Getter
public enum BizType implements DcEnum {
    // 现金收款, 冲抵帐
    UNKNOWN(0, "未知"),

    SELF(1, "自营"),

    NON_SELF(2, "非自营"),
    ;

    private final int code;
    private final String desc;

    BizType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BizType valueOf(int code) {
        for (BizType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return BizType.UNKNOWN;
    }
}