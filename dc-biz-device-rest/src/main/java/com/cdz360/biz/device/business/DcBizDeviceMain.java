package com.cdz360.biz.device.business;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import reactor.core.publisher.Hooks;

/**
 * <AUTHOR>
 * @SpringBootApplication 注解等价于以默认属性使用 @Configuration + @EnableAutoConfiguration + @ComponentScan
 * @since 2018年9月7日18:19:45
 */
//开启eureka服务端-对外提供接口时使用此注解
@EnableDiscoveryClient
//开启eureka客户端feign支持-调用外部接口时使用此注解，value为@FeignClient注解类所在的包路径
@EnableFeignClients({
        "com.cdz360.biz.device.command.facade",
        "com.cdz360.biz.device.manager.facade",
        "com.cdz360.biz.device.business.client"
})
@EnableTransactionManagement
@EnableCaching
//@ImportResource(locations = {"classpath:spring/*.xml"})
@SpringBootApplication
@ComponentScan(basePackages = {
        "com.cdz360.data.sync",
        "com.cdz360.data.cache",
        "com.cdz360.biz.device.business",
        "com.cdz360.biz.device.common"
})
@MapperScan(basePackages = {
        "com.cdz360.biz.device.business.mapper"
})
@Slf4j
public class DcBizDeviceMain //extends SpringBootServletInitializer
{

//    @Autowired
//    private StringRedisTemplate redisStringTemplate;


    public static void main(String[] args)  {
//        System.setProperty("es.set.netty.runtime.available.processors", "false");
        log.info("starting dc-biz-device-rest!!!");
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
        new SpringApplicationBuilder(DcBizDeviceMain.class).web(WebApplicationType.SERVLET).run(args);

    }



}
