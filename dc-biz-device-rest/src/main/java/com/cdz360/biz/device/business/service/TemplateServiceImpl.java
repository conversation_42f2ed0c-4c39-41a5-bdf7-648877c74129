package com.cdz360.biz.device.business.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.device.business.client.EvseCfgClient;
import com.cdz360.biz.device.business.client.MerchantFeignClient;
import com.cdz360.biz.device.business.client.UserFeignClient;
import com.cdz360.biz.device.business.constant.DelFlagConst;
import com.cdz360.biz.device.business.constant.TemplateCalculateTypeEnum;
import com.cdz360.biz.device.business.entity.convert.TemplateConvert;
import com.cdz360.biz.device.business.entity.po.SubTemplate;
import com.cdz360.biz.device.business.entity.po.Template;
import com.cdz360.biz.device.business.entity.po.WhiteCardDto;
import com.cdz360.biz.device.business.entity.po.WhiteCardRequest;
import com.cdz360.biz.device.business.entity.request.BoxSettingUpsertRequest;
import com.cdz360.biz.device.business.entity.request.SiteListRequest;
import com.cdz360.biz.device.business.entity.request.TemplateListRequest;
import com.cdz360.biz.device.business.entity.result.BoxInfoVo;
import com.cdz360.biz.device.business.entity.result.SiteDefultSettingVO;
import com.cdz360.biz.device.business.entity.result.SiteSimpleInfoVo;
import com.cdz360.biz.device.business.entity.result.SubTemplateInfoVo;
import com.cdz360.biz.device.business.entity.result.TemplateFullInfoVo;
import com.cdz360.biz.device.business.entity.result.TemplateInfoVo;
import com.cdz360.biz.device.business.entity.result.TemplateModuleVo;
import com.cdz360.biz.device.business.mapper.SiteQueryMapper;
import com.cdz360.biz.device.business.mapper.SubTemplateMapper;
import com.cdz360.biz.device.business.mapper.TemplateMapper;
import com.cdz360.biz.device.business.util.DbsAssert;
import com.cdz360.biz.device.business.util.FeignResponseValidate;
import com.cdz360.biz.device.common.dingchong.ChargeV2;
import com.cdz360.data.cache.RedisIotReadService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @date Create on 2018/12/21 16:29
 */
@Slf4j
@Service
public class TemplateServiceImpl implements TemplateService {

    /**
     * 计费模板初始版本号
     */
    private static final int FLAG_TEMPLATE_INITIAL_VERSION = 1000;
    //    /**
//     * 修改计费模板后下发计费模板定时任务组名称
//     */
//    private static final String FLAG_JOB_GROUP = "chargerlink_template_job_group";
//    /**
//     * 修改计费模板后下发计费模板定时任务触发器名称
//     */
//    private static final String FLAG_TRIGGER_GROUP = "chargerlink_template_trigger_group";
    private static final Integer FREE_CHARGE_FLAG = 1;//免费的计费模板标识
    @Autowired
    private RedisIotReadService redisIotReadService;

    //    @Autowired
//    private Scheduler scheduler;
    @Autowired
    private TemplateMapper templateMapper;
    @Autowired
    private SubTemplateMapper subTemplateMapper;

    @Autowired
    private BoxQueryService boxQueryService;
    //    @Autowired
//    private CacheService cacheService;
//    @Autowired
//    private SiteManageMapper siteManageMapper;
    @Autowired
    private SiteQueryMapper siteQueryMapper;
    @Autowired
    private BoxSettingService boxSettingService;
    @Autowired
    private SiteDefultSettingService siteDefultSettingService;
    @Autowired
    private SiteQueryService siteQueryService;
    //    @Autowired
//    private DcEventPublisherService dcEventPublisherService;
    @Autowired
    private EvseCfgClient evseCfgClient;
    @Autowired
    private MerchantFeignClient merchantFeignClient;
    @Autowired
    private UserFeignClient carUserFeignClient;

//    @Autowired
//    private DataCoreFeignClient dataCoreFeignClient;

    // 修改计费模板定时约束时间: 单位: 分钟
    @Value("${iot.cmd.timeout:10}")
    private Integer timeout;


    private String getHourAndMinute(Integer time) {
        Integer hour = time / 60;
        Integer minute = time % 60;
        return String.format("%02d:%02d", hour, minute);
    }

    /**
     * 根据设备信息下发计费模板和紧急卡或者通用配置
     *
     * @param templateInfoVo
     * @param boxInfoVo
     * @return
     */
    @Override
    public boolean notifyUpdateDeviceTemplateOrSettingByDevice(TemplateInfoVo templateInfoVo,
        BoxInfoVo boxInfoVo) {
        log.info("下发计费模板开始>>>");

//        CfgEvseParam.CfgEvse.Charge charge = null;
        List<ChargeV2> chargeV2List = new ArrayList<>();
        if (templateInfoVo != null) {
            //是否免费的计费模板
            if (FREE_CHARGE_FLAG.equals(templateInfoVo.getFreeChargeFlag())) {
//                charge.setDefaultCode(0);
//                charge.setServPrice(0L);

                ChargeV2 chargeV2 = new ChargeV2();
                chargeV2.setCode(0);
                chargeV2.setStartTime("00:00");
                chargeV2.setStopTime("24:00");
                chargeV2.setElecPrice(BigDecimal.ZERO);
                chargeV2.setServPrice(BigDecimal.ZERO);

                chargeV2List.add(chargeV2);

            } else {
                BigDecimal servicePrice = null;
                // 不支持分时计费获取子模版第一个作为服务费下发
                if (templateInfoVo.getCalculateType()
                    != TemplateCalculateTypeEnum.TYPE_TIME_RANGE_SEVR_FEE.getCode()) {
                    servicePrice = templateInfoVo.getSubTemplateList().get(0).getServicePrice();
                }
                //TODO 服务费单价和默认计费模板，这里使用的是数组第一个元素，之后需要再确定
                //已取消默认单价，服务费也放置到分时计费

                BigDecimal finalServicePrice = servicePrice;
                templateInfoVo.getSubTemplateList().stream().forEach(e -> {

                    Integer code = Integer.valueOf(e.getTariffTag());

                    ChargeV2 chargeV2 = new ChargeV2();
                    chargeV2.setCode(code);
                    chargeV2.setElecPrice(e.getPrice());
                    chargeV2.setServPrice(
                        finalServicePrice == null ? e.getServicePrice() : finalServicePrice);
                    if (e.getStartTime() != null) {
                        chargeV2.setStartTime(getHourAndMinute(e.getStartTime()));
                    }
                    if (e.getStopTime() != null) {
                        chargeV2.setStopTime(getHourAndMinute(e.getStopTime()));
                    }
                    chargeV2List.add(chargeV2);
                });

            }
        }

        BoxSettingUpsertRequest req = new BoxSettingUpsertRequest();
        //当激活桩、修改桩、修改计费模板时，下发计费模板只会是单个接单个下发
        req.setBoxOutFactoryCode(boxInfoVo.getSerialNumber());
        //        req.setCharge(cfgEvseParam.getCfgEvse().getCharge());
        req.setCharge(chargeV2List);
        req.setChargeId(templateInfoVo.getId());
        SiteDefultSettingVO siteDefultSettingVO = null;
        //场站通用配置
        if (boxInfoVo.getIsUseSiteDefaultSetting() != null
            && boxInfoVo.getIsUseSiteDefaultSetting() == 1) {
            if (boxInfoVo.getSiteId() == null) {
                siteDefultSettingVO = siteDefultSettingService.selectByBoxOutFactoryCode(
                    boxInfoVo.getSerialNumber());
            } else {
                siteDefultSettingVO = siteDefultSettingService.selectBySiteId(
                    boxInfoVo.getSiteId());
            }
            DbsAssert.isNotNull(siteDefultSettingVO, "根据桩信息未查询到对应场站的通用配置记录");
            req.setAdminPassword(siteDefultSettingVO.getAdminPassword());
            req.setLevel2Password(siteDefultSettingVO.getLevel2Password());
            req.setDayVolume(siteDefultSettingVO.getDayVolume());
            req.setNightVolume(siteDefultSettingVO.getNightVolume());
            req.setUrl(siteDefultSettingVO.getUrl());
            req.setIsTimedCharge(siteDefultSettingVO.getIsTimedCharge());
            req.setIsQueryChargeRecord(siteDefultSettingVO.getIsQueryChargeRecord());
            req.setIsNoCardCharge(siteDefultSettingVO.getIsNoCardCharge());
            req.setIsScanCharge(siteDefultSettingVO.getIsScanCharge());
            req.setIsVinCharge(siteDefultSettingVO.getIsVinCharge());
            req.setIsCardCharge(siteDefultSettingVO.getIsCardCharge());
            req.setIsQuotaEleCharge(siteDefultSettingVO.getIsQuotaEleCharge());
            req.setIsQuotaMoneyCharge(siteDefultSettingVO.getIsQuotaMoneyCharge());
            req.setIsQuotaTimeCharge(siteDefultSettingVO.getIsQuotaTimeCharge());
        }
        //绑桩时下发紧急充电卡
        WhiteCardRequest whiteCardRequest = new WhiteCardRequest();
        whiteCardRequest.setIsAbandon(false);
        whiteCardRequest.setSite(boxInfoVo.getSiteId());
        whiteCardRequest.setCardChipNoList(null);
        ListResponse<WhiteCardDto> whiteCardDtoList = carUserFeignClient.queryWhiteCardDtoBySiteList(
            whiteCardRequest);
        FeignResponseValidate.check(whiteCardDtoList);
        if (whiteCardDtoList.getData().size() > 0) {
            WhiteCardDto whiteCardDto = whiteCardDtoList.getData().get(0);//请求时只有一个siteId,结果只有一个
            req.setWhiteCards(whiteCardDto.getWhiteCardList());
            req.setWhiteCardEvses(whiteCardDto.getWhiteCardEvses());
        }

        log.info("桩配置下发参数request：{}", req.toString());
        boxSettingService.sendBoxSetting(req);
        return true;
    }


    @Override
    public TemplateInfoVo getTemplateDetailById(Long templateId) throws DcServiceException {

        log.info("根据ID获取计费模板详情。templateId: {}", templateId);

        //TODO 1-获取主模板信息
        Template template = templateMapper.selectById(templateId);
        if (template == null) {
            throw new DcArgumentException("未获取到计费模板信息");
        }
        // Assert.notNull(template, "未获取到计费模板信息");
        TemplateInfoVo templateInfo = TemplateConvert.convertToTemplateInfoVo(template);

        //TODO 2-获取子模板列表
        List<SubTemplateInfoVo> subTemplateList = new ArrayList<>();
        Set<TemplateModuleVo> templateModuleSet = new HashSet<>();

        SubTemplate query = new SubTemplate();
        query.setTemplateId(templateId);
        QueryWrapper<SubTemplate> wrapper = new QueryWrapper<>(query);

        List<SubTemplate> subList = subTemplateMapper.selectList(wrapper);
        log.info("查询到的计费信息。templateId: {}, list: {}", templateId, subList);

        if (!subList.isEmpty()) {
            //按照时间排序
            subList.sort((o1, o2) -> {
                if (o1.getStartTime() == null) {
                    return 0;
                }
                if (o2.getStartTime() == null) {
                    return 0;
                }
                return o1.getStartTime() > o2.getStartTime() ? 0 : 1;
            });

            for (SubTemplate subTemplate : subList) {
                TemplateModuleVo templateModuleVo = new TemplateModuleVo();
                templateModuleVo.setTariffTag(subTemplate.getTariffTag());
                templateModuleVo.setModulePrice(subTemplate.getPrice());
                templateModuleSet.add(templateModuleVo);
                subTemplateList.add(TemplateConvert.convertToSubTemplateInfoVo(subTemplate));
            }
        }

        templateInfo.setTemplateModuleList(templateModuleSet);
        templateInfo.setSubTemplateList(subTemplateList);

        log.info("计费模板详情。templateId: {}, list: {}", templateId, subList);

        return templateInfo;
    }

    @Override
    public TemplateInfoVo getTemplateById(Long templateId) throws DcServiceException {
        Assert.notNull(templateId, "计费模板ID不能为空");
        Template template = templateMapper.selectById(templateId);
        Assert.notNull(template, "未获取到计费模板信息");
        TemplateInfoVo templateInfo = TemplateConvert.convertToTemplateInfoVo(template);
        return templateInfo;
    }

    @Override
    public TemplateInfoVo getLatestTemplateDetailByCode(String code) throws DcServiceException {
        log.info("根据计费模板编号，获取最新的计费模板详情[code={}]", code);
        Template template = templateMapper.getLatestTemplateDetailByCode(code);
        Assert.notNull(template, "未获取到计费模板信息");
        return getTemplateDetailById(template.getId());
    }

    @Override
    public TemplateInfoVo getTemplateDetailByBcId(String evseNo) throws DcServiceException {
        log.info("根据桩编号，获取设备关联的计费模板详情[evseNo={}]", evseNo);

        EvseVo evse = this.redisIotReadService.getEvseRedisCache(evseNo);
        if (evse == null) {
            throw new DcServiceException("桩未接入云平台");
        } else if (evse.getPriceCode() == null || evse.getPriceCode() < 1) {
            throw new DcServiceException("桩未配置价格模板");
        }
        return getTemplateDetailById(evse.getPriceCode());
    }

    @Override
    public TemplateFullInfoVo getFullTemplateDetailByBcId(String evseNo) {
        log.info("根据桩号，获取设备关联的计费模板完整详情[bcId={}]", evseNo);

        EvseVo evse = this.redisIotReadService.getEvseRedisCache(evseNo);
        if (evse == null) {
            throw new DcServiceException("桩未接入云平台");
        } else if (evse.getPriceCode() == null || evse.getPriceCode() < 1) {
            throw new DcServiceException("桩未配置价格模板");
        }
        return getFullTemplateDetailById(evse.getPriceCode());
    }

    private TemplateFullInfoVo getFullTemplateDetailById(Long templateId)
        throws DcServiceException {
        log.info("根据ID获取计费模板完整详情[templateId={}]", templateId);
        //TODO 1-获取主模板信息
        Template template = templateMapper.selectById(templateId);
        Assert.notNull(template, "未获取到计费模板信息");
        TemplateFullInfoVo templateInfo = TemplateConvert.convertToTemplateFullInfoVo(template);

        //TODO 2-获取子模板列表
        List<SubTemplateInfoVo> subTemplateList = null;
        {
            SubTemplate query = new SubTemplate();
            query.setTemplateId(templateId);
            QueryWrapper<SubTemplate> wrapper = new QueryWrapper<>(query);
            List<SubTemplate> subList = subTemplateMapper.selectList(wrapper);
            log.info("查询到的计费信息为==>{}", subList);

            if (!subList.isEmpty()) {
                //按照时间排序
                subList.sort((o1, o2) -> {
                    if (o1.getStartTime() == null) {
                        return 0;
                    }
                    if (o2.getStartTime() == null) {
                        return 0;
                    }
                    return o1.getStartTime() > o2.getStartTime() ? 0 : 1;
                });
                subTemplateList = new ArrayList<>();
                for (SubTemplate subTemplate : subList) {
                    subTemplateList.add(TemplateConvert.convertToSubTemplateInfoVo(subTemplate));
                }
            }
        }

        SubTemplateInfoVo curSubTemplate = getCurSubTemplate(subTemplateList);
        if (curSubTemplate != null) {
            BigDecimal servicePrice = curSubTemplate.getServicePrice();
            templateInfo.setCurChargePrice(curSubTemplate.getPrice().add(servicePrice));//价格= 电价+服务费
            templateInfo.setCurChargePriceDesc(
                TemplateConvert.generatePriceDesc(curSubTemplate.getPrice().add(servicePrice)
                    , curSubTemplate.getScale()
                    , templateInfo.getCalculateUnit()));
        }

        templateInfo.setSubTemplateList(subTemplateList);
        return templateInfo;
    }


    /**
     * 获取跟当前时间匹配的子模板信息
     *
     * @param subTemplateList
     * @return
     */
    private SubTemplateInfoVo getCurSubTemplate(List<SubTemplateInfoVo> subTemplateList) {
        int curMin = LocalDateTime.now().getHour() * 60 + LocalDateTime.now().getMinute();
        if (!CollectionUtils.isEmpty(subTemplateList)) {
            for (SubTemplateInfoVo subTemplateInfoVo : subTemplateList) {
                if (subTemplateInfoVo.getStartTime() != null
                    && subTemplateInfoVo.getStopTime() != null
                    && subTemplateInfoVo.getStartTime() <= curMin
                    && subTemplateInfoVo.getStopTime() >= curMin) {
                    subTemplateInfoVo.setCurrentFlag(true);
                    return subTemplateInfoVo;
                }
            }
        }
        return null;
    }


    /**
     * 接口逻辑有误，分页total错误，keywords参数无效 由getTemplateInfoList方法代替
     *
     * @param request
     * @return
     * @throws DcServiceException
     */
    @Override
    public ListResponse<TemplateInfoVo> getPagedTemplateInfoList(TemplateListRequest request)
        throws DcServiceException {
        log.info("分页获取计费模板列表[request={}]", JsonUtils.toJsonString(request));
        request.setDeleteFlag(DelFlagConst.NORMAL);

        //FIXME 1.1 查询当前商户下的计费模版
        Page<TemplateInfoVo> pageInfo = PageHelper.startPage(request.getPage(), request.getRows(),
            true);
        List<TemplateInfoVo> list = templateMapper.getTemplateList(request);
        log.info("查询当前商户下的计费模版----------------------{}", JsonUtils.toJsonString(list));

        //FIXME 1.2 绑定设备时共用这个接口 传参为单个商户ID
        if (CollectionUtils.isEmpty(request.getCommercialIdList())) {
            return new ListResponse<>(list, pageInfo.getTotal());
        }

        //FIXME 1.3 查询站点列表 为空 表示集团商户没有指定站点 返回当前商户下的计费模板
        SiteListRequest siteListRequest = new SiteListRequest();
        siteListRequest.setCommercialIdList(request.getCommercialIdList());
        List<SiteSimpleInfoVo> siteSimpleList = siteQueryMapper.getSiteSimpleList(siteListRequest);
        log.info("站点列表--------------------{}", JsonUtils.toJsonString(siteSimpleList));
        if (CollectionUtils.isEmpty(siteSimpleList)) {
            return new ListResponse<>(list, pageInfo.getTotal());
        }
        List<Long> templateList = new ArrayList<>();
        for (SiteSimpleInfoVo siteSimpleInfoVo : siteSimpleList) {
            templateList.add(siteSimpleInfoVo.getTemplateId());
        }
        log.info("计费模板请求参数------------------{}", JsonUtils.toJsonString(templateList));
        // 查询绑定站点的计费模板列表
        List<TemplateInfoVo> templateListById = templateMapper.getTemplateListById(templateList);
        log.info("查询绑定站点的计费模板列表----------------{}",
            JsonUtils.toJsonString(templateListById));

        //FIXME 1.4 处理当前商户下 和 集团下发的计费模板
        List<TemplateInfoVo> differentTemplateInfoVos = new ArrayList<>();
        for (TemplateInfoVo templateInfoVo : templateListById) {
            if (!list.contains(templateInfoVo)) {
                templateInfoVo.setIsSuperior(true);
                differentTemplateInfoVos.add(templateInfoVo);
            }
        }
        log.info("当前商户下和集团商户下不同计费模板信息-------------{}",
            JsonUtils.toJsonString(differentTemplateInfoVos));

        list.addAll(differentTemplateInfoVos);
        HashSet hashSet = new HashSet<>(list);
        List<TemplateInfoVo> templateInfoVos = new ArrayList<>(hashSet);
        log.info("处理完成的计费模板列表----------------------{}",
            JsonUtils.toJsonString(templateInfoVos));
        return new ListResponse<>(templateInfoVos, pageInfo.getTotal());


    }

    /**
     * 充电管理平台 营销 分页获取计费模板列表
     *
     * @param request
     * @return
     * @throws DcServiceException
     */
    @Override
    public ListResponse<TemplateInfoVo> getTemplateInfoList(TemplateListRequest request
//, String token
    ) throws DcServiceException {
        log.info("分页获取计费模板列表[request={}]", JsonUtils.toJsonString(request));
        request.setDeleteFlag(DelFlagConst.NORMAL);
        //ObjectResponse<Commercial> res = merchantFeignClient.getCommercialByToken(token);
        //FeignResponseValidate.check(res);
        //Commercial commercial = res.getData();
        request.setCurrentCommId(request.getOpCommId());
        //FIXME 1.1 查询当前商户下的计费模版
        Page<TemplateInfoVo> pageInfo = PageHelper.startPage(request.getPage(), request.getRows(),
            true);
        List<TemplateInfoVo> list = templateMapper.getTemplateListAndBeAuthorized(request);
        list.forEach(e -> {
            // 商户仅仅可以修改自己创建的计费模板
            if (e.getCommercialId().equals(request.getOpCommId())) {
                e.setIsModify(1);
            } else {
                e.setIsModify(0);
            }
        });
        log.info("查询当前商户下的计费模版----------------------{}", JsonUtils.toJsonString(list));

        return new ListResponse<>(list, pageInfo.getTotal());

    }


    @Override
    public void enable(Long id, @NotNull Boolean enable) {
        log.info("计费模板使能操作: id = {}, enable = {}", id, enable);
        TemplateInfoVo template = templateMapper.findById(id, false);
        if (null == template) {
            log.info("该Id对应的计费模板不存在");
            throw new DcArgumentException("该Id对应的计费模板不存在, 请确认计费模板Id有效性");
        }

        if (null == enable) {
            log.info("使能参数缺失");
            throw new DcArgumentException("使能参数缺失，请提供使能入参值");
        }

        if (enable) {
            if (!template.getEnable()) { // 原来禁用时才操作数据库
                templateMapper.updateEnable(id, true);
            }
            log.info("计费模板启用成功");
            return;
        }

        // 未被使用的计费模板以及定时下发的计费模板可以禁用
        if (this.templateUsed(template)) {
            log.info("该计费模板不支持禁用操作");
            throw new DcServiceException("该计费模板不支持禁用操作");
        }

        templateMapper.updateEnable(id, false);
        log.info("计费模板禁用成功");
    }

    public boolean templateUsed(TemplateInfoVo template) {
        // TODO: 从iotWorker中获取查询
        // 未被使用的计费模板以及定时下发的计费模板可以禁用
        return false;
    }
}
