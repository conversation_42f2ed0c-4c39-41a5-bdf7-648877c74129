package com.cdz360.biz.device.business.constant;

/**
 * 计费模板的订单收费方式
 *
 * <AUTHOR>
 * @date Create on 2018/11/2 16:01
 */
public enum TemplateChargingTypeEnum {

    /**
     * 按照固定收费标准收费
     */
    CHARGE_CONSTANT(0),
    /**
     * 按照实时计算的价格收费
     */
    CHARGE_WITH_REAL_TIME(1),;
    //
    ;
    public int value;

    TemplateChargingTypeEnum(int value) {
        this.value = value;
    }

    /**
     * 根据value值获取对应的枚举
     *
     * @param value
     * @return
     */
    public static TemplateChargingTypeEnum getEnumByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (TemplateChargingTypeEnum typeEnum : TemplateChargingTypeEnum.values()) {
            if (value == typeEnum.value) {
                return typeEnum;
            }
        }
        return null;
    }
}
