package com.cdz360.biz.device.business.constant;

/**
 * @Classname DbsUrlConstant
 * @Description 请求Url映射常量
 * @Date 2019/6/14 8:29
 * @Created by <PERSON><PERSON><PERSON>
 * @Email <EMAIL>
 */
public interface DbsUrlConstant {

    // 站点默认配置相关接口
    String URL_SITE_DEFULT_SELECT_PAGE = "/api/sitedefultsetting/selectPageSite";
    String URL_SITE_DEFULT_SELECT_BYSITE = "/api/sitedefultsetting/selectBySiteId/{siteId}";
    String URL_SITE_DEFULT_INSERT = "/api/sitedefultsetting/insert";
    String URL_SITE_DEFULT_UPDATE = "/api/sitedefultsetting/update";
    String URL_SITE_DEFULT_DELETE = "/api/sitedefultsetting/delete/{siteId}";

    // 桩配置相关接口地址
    String URL_BOXSETTING_SELECT_PAGE = "/api/boxsetting/selectPagedList";
    String URL_BOXSETTING_SELECT_BY_ID = "/api/boxsetting/select/{id}";
    String URL_BOXSETTING_INSERT = "/api/boxsetting/insert";
    String URL_BOXSETTING_UPATTE = "/api/boxsetting/update";
    String URL_BOXSETTING_UPATTE_BY_EVSEID = "/api/boxsetting/updateByEvseId";

    String URL_BOXSETTING_UPATTE_QRCODE = "/api/boxsetting/updateQRCode";
    String URL_BOXSETTING_SEND_QRCODE_BY_ID = "/api/boxsetting/sendQRCodeById";
    String URL_BOXSETTING_SEND_FAIL_QRCODE = "/api/boxsetting/sendFailQRCode";
    String URL_BOXSETTING_DELETE = "/api/boxsetting/delete/{id}";
    String URL_BOXSETTING_DELETE_BATCH = "/api/boxsetting/deleteBatch";

    String URL_BOXSETTING_SEND = "/api/boxsetting/sendBoxSetting";
    String URL_BOXSETTING_SEND_BATCH = "/api/boxsetting/sendBatchBoxSetting";
    String URL_BOXSETTING_SELECT_BY_TOKEN = "/api/boxsetting/getByCommId";
    String URL_BOXSETTING_SEND_WHITECARD = "/api/boxsetting/sendWhiteCard";
    String URL_BOXSETTING_URGENCY_CARD_DETAIL_EVSE_LIST = "/api/boxsetting/urgencyCardsDetailEvseList";
    String URL_BOXSETTING_SEND_BATCH_WHITECARD = "/api/boxsetting/sendBatchWhiteCards";
    String URL_BOXSETTING_UPDATE_STATUS_ON_EVSE_ID_AND_STATUS = "/api/boxsetting/updateStatusOnEvseIdAndStatus";

    String URL_BOXSETTING_SELECT_BY_STATIONCODE_WHITECARDSTATUS = "/api/boxsetting/selectCountByStationCodeAndWhiteCardStatus";
    String URL_BOXSETTING_GET_EVSE_PASSCODE = "/api/boxsetting/getEvsePasscode/{evseNo}";
    String URL_BOXSETTING_DOWN_SETTING_TO_EVSE = "/api/boxsetting/downSetting2Evse";

    // 升级包相关接口
    String URL_EVSE_BUNDLE_PAGE = "/api/evsebundle/page";
    String URL_EVSE_BUNDLE_LIST = "/api/evsebundle/list";
    String URL_EVSE_BUNDLE_UPLOAD = "/api/evsebundle/upload";
    String URL_EVSE_BUNDLE_DELETE = "/api/evsebundle/delete/{id}";
    String URL_EVSE_BUNDLE_GET_BY_VERSION = "/api/evsebundle/getByVer/{version}";



}