package com.cdz360.biz.device.business.service;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.device.business.entity.request.SiteGeoListRequest;
import com.cdz360.biz.device.business.entity.request.SiteSimpleInfoRequest;
import com.cdz360.biz.device.business.entity.result.SiteDetailInfoVo;
import com.cdz360.biz.device.business.entity.result.SiteSimpleInfoVo;

/**
 * <p>
 * 站点查询服务
 * </p>
 *
 * <AUTHOR>
 * @since 2018-07-23
 */
public interface SiteQueryService {


    /**
     * 获取站点信息
     *
     * @param request
     * @return
     * @throws DcServiceException
     */
    SiteDetailInfoVo getSiteDetailBySiteId(SiteGeoListRequest request) throws DcServiceException;

    /**
     * 根据站点ID获取站点简单信息
     *
     * @param request
     * @return
     * @throws DcServiceException
     */
    SiteSimpleInfoVo getSiteSimpleInfoById(SiteSimpleInfoRequest request) throws DcServiceException;


}
