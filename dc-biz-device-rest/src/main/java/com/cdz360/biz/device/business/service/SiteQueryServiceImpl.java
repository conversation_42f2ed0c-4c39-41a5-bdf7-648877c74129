package com.cdz360.biz.device.business.service;


import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.device.business.client.AuthCoreClient;
import com.cdz360.biz.device.business.client.MerchantFeignClient;
import com.cdz360.biz.device.business.client.UserFeignClient;
import com.cdz360.biz.device.business.constant.DBSCacheKeyEnum;
import com.cdz360.biz.device.business.entity.convert.SiteConvert;
import com.cdz360.biz.device.business.entity.po.Site;
import com.cdz360.biz.device.business.entity.request.SiteGeoListRequest;
import com.cdz360.biz.device.business.entity.request.SiteSimpleInfoRequest;
import com.cdz360.biz.device.business.entity.result.SiteDetailInfoVo;
import com.cdz360.biz.device.business.entity.result.SiteSimpleInfoVo;
import com.cdz360.biz.device.business.mapper.SiteQueryMapper;
import com.cdz360.biz.device.business.util.MapUtils;
import com.cdz360.biz.device.common.service.CacheService;
import com.cdz360.data.cache.RedisIotReadService;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 站点查询服务
 *
 * <AUTHOR>
 * @since 2018/7/24
 */
@Slf4j
@Service
public class SiteQueryServiceImpl implements SiteQueryService {

    @Autowired
    private SiteQueryMapper siteQueryMapper;
    @Autowired
    private CacheService cacheService;
    //    @Autowired
//    private AreaInfoService areaInfoService;
//    @Autowired
//    private PropertiesConfig propertiesConfig;

    @Autowired
    private AuthCoreClient authCoreClient;
    @Autowired
    private MerchantFeignClient merchantFeignClient;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private UserFeignClient userFeignClient;


    @Override
    public SiteDetailInfoVo getSiteDetailBySiteId(SiteGeoListRequest request)
        throws DcServiceException {
        //TODO 1-从缓存中获取
        SiteDetailInfoVo detailInfo = null;
        try {
            String json = (String) cacheService.getValue(
                DBSCacheKeyEnum.STATION_DETAIL, request.getSiteId());
            detailInfo = JsonUtils.fromJson(json, SiteDetailInfoVo.class);
        } catch (Exception e) {
        }
        if (detailInfo == null) {
            //TODO 1.1-缓存中不存在，从数据库中获取
            Site site = siteQueryMapper.selectById(request.getSiteId());
            if (site == null) {
                log.error("未查询到站点信息[siteId={}]", request.getSiteId());
                throw new DcServiceException("未查询到站点信息");
            }
            log.info("先获取到站点信息[site={}]", JsonUtils.toJsonString(site));
            detailInfo = SiteConvert.convertToSiteVo(site);
            detailInfo.setSiteNo(site.getSiteNo());
            detailInfo.setFrozenAmount(site.getFrozenAmount());
//            detailInfo.setProvinceName(areaInfoService.getProvinceNameByCode(detailInfo.getProvince()));
//            detailInfo.setCityName(areaInfoService.getCityNameByCode(detailInfo.getCity()));
//            detailInfo.setAreaName(areaInfoService.getAreaNameByCode(detailInfo.getArea()));
            //统计站点插座详情
            Map<String, Integer> chargerStatusMap = new HashMap<>(PlugStatus.values().length);
            for (PlugStatus statusEnum : PlugStatus.values()) {
                chargerStatusMap.put(statusEnum.name(), 0);
            }

            List<PlugVo> plugCacheList = redisIotReadService.listPlugBySiteId(request.getSiteId());

            //TODO 从缓存中获取
            //List connectorStatusList = cacheService.getAllHash(DeviceCacheKeyEnum.STATION_ID_REL_CONNECTOR_STATUS_4, request.getSiteId());
            if (CollectionUtils.isNotEmpty(plugCacheList)) {
                for (PlugVo plugCache : plugCacheList) {
                    if (plugCache != null && plugCache.getStatus() != null) {
                        int count = chargerStatusMap.get(plugCache.getStatus().name());
                        chargerStatusMap.put(plugCache.getStatus().name(), count + 1);
                    } else {
                        log.warn("异常的缓存数据. plugCache = {}", plugCache);
                    }
                }
            }
            detailInfo.setChargerStatusMap(chargerStatusMap);

            //TODO 1.2-站点信息放入缓存中
            cacheService.setValue(DBSCacheKeyEnum.STATION_DETAIL, request.getSiteId(),
                JsonUtils.toJsonString(detailInfo));
        }
        //增加所属集团商户信息
        ObjectResponse<JsonNode> rez = authCoreClient.getMaxCommercialByCommId(
            detailInfo.getOperateId());
        if (rez.getData() != null) {
            JsonNode jsonObject = rez.getData();
            detailInfo.setMaxCommercialId(jsonObject.get("id").asLong());
            detailInfo.setMaxCommercialName(jsonObject.get("commName").asText());
        }
        if (detailInfo.getLatitude() == null || detailInfo.getLongitude() == null
            || request.getLatitude() == null || request.getLongitude() == null) {
            return detailInfo;
        }
        // 计算与传入经纬度的距离
        double distance = MapUtils.getDistance(detailInfo.getLatitude().doubleValue(),
            detailInfo.getLongitude().doubleValue(),
            request.getLatitude(), request.getLongitude());
        detailInfo.setDistance(distance);
//        // 获取预计行驶时间
//        Double duration = this.getDuration(detailInfo.getLatitude().doubleValue(),
//                detailInfo.getLongitude().doubleValue(),
//                request.getLatitude(), request.getLongitude());
//        detailInfo.setDuration(duration);
        return detailInfo;
    }

//    /**
//     * 获取预计驾驶时间
//     *
//     * @param originLatitude
//     * @param originLongitude
//     * @param destinationLatitude
//     * @param destinationLongitude
//     * @return
//     */
//    private Double getDuration(Double originLatitude, Double originLongitude, Double destinationLatitude, Double destinationLongitude) {
//        String sb = propertiesConfig.getMapDrivingUrl() +
//                "&origin=" + originLongitude + "," + originLatitude +
//                "&destination=" + destinationLongitude + "," + destinationLatitude;
//
//        String resultStr = HttpUtils.sendGet(sb);
////        log.info("获取到的驾车路径规划结果[resultStr:{}]", resultStr);
//        if (StringUtils.isBlank(resultStr)) {
//            return null;
//        }
//        JsonNode resultJson = JsonUtils.fromJson(resultStr);
//        if (resultJson == null || resultJson.get("status") == null || resultJson.get("status").asInt() == 0) {
//            return null;
//        }
//        JsonNode route = resultJson.get("route");
//        if (route == null) {
//            return null;
//        }
//        JSONArray paths = route.get("paths");
//        if (paths == null || paths.size() == 0) {
//            return null;
//        }
//        return paths.getJSONObject(0).getDouble("duration");
//    }


    @Override
    public SiteSimpleInfoVo getSiteSimpleInfoById(SiteSimpleInfoRequest request)
        throws DcServiceException {
        //TODO 1-获取站点信息
        Site entity = siteQueryMapper.selectById(request.getSiteId());
        if (entity == null) {
            log.error("未查询到站点信息[siteId={}]", request.getSiteId());
//            throw new DcServiceException("未查询到站点信息");
            return null;
        }
        SiteSimpleInfoVo siteInfo = SiteConvert.convertToSiteSimpleInfoVo(entity);
//        siteInfo.setProvinceName(areaInfoService.getProvinceNameByCode(siteInfo.getProvince()));
//        siteInfo.setCityName(areaInfoService.getCityNameByCode(siteInfo.getCity()));
        return siteInfo;
    }


}