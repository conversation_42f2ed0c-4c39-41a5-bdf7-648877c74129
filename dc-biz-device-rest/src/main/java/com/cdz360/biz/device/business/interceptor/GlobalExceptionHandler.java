package com.cdz360.biz.device.business.interceptor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Classname GlobalExceptionInterceptor
 * @Description 全局异常处理
 * @Date 2019/6/6 16:48
 * @Created by JLei
 * @Email <EMAIL>
 */
@Slf4j
@ControllerAdvice(annotations = RestController.class)
public class GlobalExceptionHandler {
    private final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);


    @ExceptionHandler
    @ResponseBody
    public BaseResponse handle(Exception ex) {
        logger.error("系统错误（device-business）, error = {}", ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
        result.setError("系统错误（device-business）");
        if (ex instanceof IllegalArgumentException) {
            // Assert 失败的异常
            result.setStatus(2000);
            result.setError(ex.getMessage());
        }
        // TODO: 需要按异常类型做处理, 返回不同的status和error

        return result;
    }


    @ExceptionHandler({DcException.class})
    @ResponseBody
    public BaseResponse handleDcException(DcException ex) {
        log.warn(ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(ex.getStatus());
        result.setError(ex.getMessage());
        return result;
    }

    @ExceptionHandler({IllegalArgumentException.class})
    @ResponseBody
    public BaseResponse handleAssertException(IllegalArgumentException ex) {
        log.warn(ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR);
        result.setError(ex.getMessage());
        return result;
    }

//    @ExceptionHandler({ChargerlinkException.class})
//    @ResponseBody
//    public BaseResponse handleChargerlinkException(ChargerlinkException ex) {
//        log.warn(ex.getMessage(), ex);
//        BaseResponse result = new BaseResponse();
//        result.setStatus(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR);
//        result.setError(ex.getMessage());
//        return result;
//    }
}
