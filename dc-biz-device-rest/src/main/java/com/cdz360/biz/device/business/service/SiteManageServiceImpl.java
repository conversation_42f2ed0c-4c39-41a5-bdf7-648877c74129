package com.cdz360.biz.device.business.service;

//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.cdz360.base.utils.CollectionUtils;
//import com.cdz360.biz.device.business.entity.po.Site;
//import com.cdz360.biz.device.business.mapper.SiteManageMapper;
//import com.cdz360.biz.device.business.service.core.DcEventPublisherService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
///**
// * 站点管理服务
// *
// * <AUTHOR>
// * @since 2018/7/23 18:00
// */
//@Slf4j
//@Service
//public class SiteManageServiceImpl //implements SiteManageService
//{
//
//
//    @Autowired
//    private SiteManageMapper siteManageMapper;
//
//    @Autowired
//    private DcEventPublisherService dcEventPublisherService;
//
//
//    /**
//     * 发送所有场站的MQ消息
//     */
////    @Override
//    public void publishAllSiteInfo() {
//        long pageIdx = 1L;
//        int size = 100;
//        List<Site> list;
//        do {
//            Site query = new Site();
//            IPage<Site> page = siteManageMapper.selectPage(new Page<>(pageIdx, size), new QueryWrapper<>(query));
//            list = page.getRecords();
//            pageIdx += 1L;
//            list.stream().forEach(s -> dcEventPublisherService.publishSiteInfo(s.getId()));
//        } while (CollectionUtils.isNotEmpty(list));
//
//    }
//
//
//}
