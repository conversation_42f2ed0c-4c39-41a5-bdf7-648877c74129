package com.cdz360.biz.device.business.service;

import com.cdz360.biz.device.business.entity.request.SiteDefultSettingRequest;
import com.cdz360.biz.device.business.entity.result.SiteDefultSettingVO;

import java.util.Map;

/**
 * @Interfacename SiteDefultSettingService
 * @Description TODO
 * @Date 2019/6/11
 * @Created by wangzheng
 */
public interface SiteDefultSettingService {
    /**
     * 根据条件查询对象
     * @param siteId
     * @return
     */
    SiteDefultSettingVO selectBySiteId(String siteId);

    /**
     * 分页查询对象列表
     * @param request
     * @return
     */
    Map<String, Object> selectPageSiteDefultSetting(SiteDefultSettingRequest request);

    /**
     * 新增
     * @param request
     * @return
     */
    Integer insertSiteDefultSetting(SiteDefultSettingRequest request);

    /**
     * 修改
     * @param request
     * @return
     */
    Integer modifySiteDefultSetting(SiteDefultSettingRequest request);

    /**
     * del By siteId
     * @param siteId
     * @return
     */
    Integer deleteSiteDefultSetting(String siteId);

    /**
     * 通过设备序列号/桩号查询场站通用配置
     * @param boxOutFactoryCode
     * @return
     */
    SiteDefultSettingVO selectByBoxOutFactoryCode(String boxOutFactoryCode);

}
