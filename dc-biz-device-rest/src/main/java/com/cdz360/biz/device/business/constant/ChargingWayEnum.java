package com.cdz360.biz.device.business.constant;

/**
 * 收费方式 0:桩端计费，后付费; 1:桩端计费，预付费; 2:后台计费
 *
 * <AUTHOR>
 * @date Create on 2019/03/27 10:10
 */
public enum ChargingWayEnum {
    CHARGING_WAY_BEHIND(0, "桩端计费，后付费"),
    CHARGING_WAY_PRE(1, "桩端计费，预付费"),
    CHARGING_WAY_PLATFORM(2, "后台计费"),
    ;

    private Integer value;
    private String valueName;

    ChargingWayEnum(Integer value, String valueName) {
        this.value = value;
        this.valueName = valueName;
    }

    public Integer getValue() {
        return value;
    }

    public String getValueName() {
        return valueName;
    }
}
