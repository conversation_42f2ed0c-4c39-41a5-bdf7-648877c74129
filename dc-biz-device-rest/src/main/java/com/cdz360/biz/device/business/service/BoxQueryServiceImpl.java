package com.cdz360.biz.device.business.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.biz.device.business.client.IotDeviceMmgClient;
import com.cdz360.biz.device.business.entity.request.BoxListRequest;
import com.cdz360.biz.device.business.entity.result.BoxInfoVo;
import com.cdz360.biz.device.business.entity.result.BoxSettingInfoVo;
import com.cdz360.biz.device.business.entity.result.OnlineBoxVo;
import com.cdz360.biz.device.business.mapper.BsBoxQueryMapper;
import com.cdz360.biz.device.common.constant.DeviceStatusEnum;
import com.cdz360.biz.device.common.service.CacheService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 设备查询服务
 *
 * <AUTHOR>
 * @date Create on 2018/7/6 11:21
 */
@Service
public class BoxQueryServiceImpl implements BoxQueryService {

    private static final Logger logger = LoggerFactory.getLogger(BoxQueryService.class);


    private static final Integer SETTING_STATUS_SEND_OK = 1;
    // 支持分时计费协议最低版本
    private static final Integer SERVFEE_TIME_DIVISION_PROTOVER = 350;

    @Autowired
    private BsBoxQueryMapper bsBoxQueryMapper;

    @Autowired
    private CacheService cacheService;
    @Autowired
    private IotDeviceMmgClient iotDeviceMmgClient;


    @Autowired
    private RedisIotReadService redisIotReadService;




    @Override
    public ListResponse<BoxInfoVo> getPagedBoxSimpleList(BoxListRequest request) throws DcServiceException {
        logger.info("根据站点ID分页获取站点下的设备列表[request={}]", JsonUtils.toJsonString(request));
        Page<BoxInfoVo> pageInfo = PageHelper.startPage(request.getPage(), request.getRows(), true);
        List<BoxInfoVo> list = bsBoxQueryMapper.getBoxSimpleList(request);

        if (CollectionUtils.isNotEmpty(list)) {
            // 根据桩ID列表获取桩信息列表
            List<String> evseIds = list.stream().map(BoxInfoVo::getSerialNumber).collect(Collectors.toList());

            List<EvseVo> evseList = redisIotReadService.getEvseList(evseIds);
            Map<String, EvseVo> evseMap = evseList.stream().collect(Collectors.toMap(EvseVo::getEvseNo, v -> v));

            list.forEach(boxInfo -> {
//                EvseVo cache = redisIotReadService.getEvseRedisCache(boxInfo.getSerialNumber());
                EvseVo cache = evseMap.get(boxInfo.getSerialNumber());
                if (cache == null) {
                    boxInfo.setEvseStatus(EvseStatus.OFFLINE);
                    boxInfo.setStatus(DeviceStatusEnum.STATUS_OFFLINE.getCode());
                } else {
                    boxInfo.setEvseStatus(cache.getStatus());
                    boxInfo.setStatus(DeviceStatusEnum.convertFromEvseStatus(cache.getStatus()).getCode());
                }

                if (SETTING_STATUS_SEND_OK.equals(boxInfo.getSendStatus())) {
                    //最新下发模板 赋值给 生效模板
                    boxInfo.setActiveTemplateName(boxInfo.getTemplateName());
                    boxInfo.setActiveTemplateId(boxInfo.getTemplateId());
                } else {
                    //TODO 生效模板 暂时设置为空
                    boxInfo.setActiveTemplateName(null);
                    boxInfo.setActiveTemplateId(null);
                }

                Optional<EvseVo> evseVoOpt = evseList.stream()
                        .filter(evseVo -> boxInfo.getSerialNumber().equals(evseVo.getEvseNo()))
                        .findFirst();

                Boolean supflag = false;
                if (evseVoOpt.isPresent()) {
                    if (!(evseVoOpt.get().getProtocolVer() == null)) {
                        supflag = evseVoOpt.get().getProtocolVer() >= SERVFEE_TIME_DIVISION_PROTOVER
                                && EvseProtocolType.DC.equals(evseVoOpt.get().getProtocol());
                    }
                }
                boxInfo.setServFeeTimeDivision(supflag);
            });
        }

        return new ListResponse<>(list, pageInfo.getTotal());
    }

    @Override
    public ListResponse<BoxSettingInfoVo> getPagedBoxSettingSimpleList(BoxListRequest request) throws DcServiceException {
        Page<BoxInfoVo> pageInfo = PageHelper.startPage(request.getPage(), request.getRows(), true);
        List<BoxSettingInfoVo> list = bsBoxQueryMapper.BoxSettingSimpleList(request);
        return new ListResponse<>(list, pageInfo.getTotal());
    }



    @Override
    public List<String> getAllBoxBySiteId(String siteId) {
        return bsBoxQueryMapper.getAllBoxBySiteId(siteId);
    }

    /**
     * 查询站点下在线场站列表
     *
     * @param request
     * @return
     */
    @Override
    public OnlineBoxVo getOnlineBoxSimpleList(BoxListRequest request) {
        logger.info("根据站点ID站点下的在线的设备列表[request={}]", JsonUtils.toJsonString(request));
        List<BoxInfoVo> list = bsBoxQueryMapper.getBoxSimpleList(request);
        OnlineBoxVo onlineBoxVo = new OnlineBoxVo();
        onlineBoxVo.setSiteId(request.getSiteId());
        if (list == null || list.size() <= 0) {
            return onlineBoxVo;
        }
        List<BoxInfoVo> onlineList = new ArrayList<>();
        List<String> offlineEvseIds = new ArrayList<>();
        list.forEach(boxInfo -> {
            EvseVo cache = redisIotReadService.getEvseRedisCache(boxInfo.getSerialNumber());
            //DeviceStatusInfo statusInfo = getBoxStatusByDeviceId(boxInfo.getDeviceId());
            if (cache == null) {
                offlineEvseIds.add(boxInfo.getSerialNumber());
            } else {
                DeviceStatusEnum status = DeviceStatusEnum.convertFromEvseStatus(cache.getStatus());
                if (status == DeviceStatusEnum.STATUS_ONLINE) {
                    onlineList.add(boxInfo);
                } else {
                    offlineEvseIds.add(boxInfo.getSerialNumber());
                }
            }

        });

        onlineBoxVo.setOnlineBoxInfoVos(onlineList);
        onlineBoxVo.setOfflineEvseIds(offlineEvseIds);
        return onlineBoxVo;
    }


}
