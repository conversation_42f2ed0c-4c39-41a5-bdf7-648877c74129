package com.cdz360.biz.device.business.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.device.business.entity.request.BoxListRequest;
import com.cdz360.biz.device.business.entity.result.BoxInfoVo;
import com.cdz360.biz.device.business.entity.result.BoxSettingInfoVo;
import com.cdz360.biz.device.business.entity.result.OnlineBoxVo;

import java.util.List;

/**
 * 设备查询服务
 *
 * <AUTHOR>
 * @date Create on 2018/7/5 22:50
 */
public interface BoxQueryService {


    /**
     * 根据站点ID分页获取站点下的设备列表
     *
     * @param query
     * @return
     * @throws DcServiceException
     */
    ListResponse<BoxInfoVo> getPagedBoxSimpleList(BoxListRequest query) throws DcServiceException;

    /**
     * 根据站点ID分页获桩&桩配置信息列表
     *
     * @param query
     * @return
     * @throws DcServiceException
     */
    ListResponse<BoxSettingInfoVo> getPagedBoxSettingSimpleList(BoxListRequest query) throws DcServiceException;



    /**
     * 根据场站获取使用场站默认桩配置的桩序列号/桩号(serialNumber)
     *
     * @param siteId
     * @return
     */
    List<String> getAllBoxBySiteId(String siteId);


    OnlineBoxVo getOnlineBoxSimpleList(BoxListRequest request);


}
