package com.cdz360.biz.device.business.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.device.business.client.IotDeviceMmgClient;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskDetailRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskInfoRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskListRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskRequest;
import com.cdz360.biz.device.business.entity.result.UpgradeTaskDetailVo;
import com.cdz360.biz.device.business.entity.result.UpgradeTaskInfoVo;
import com.cdz360.biz.device.business.entity.result.UpgradeTaskVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname UpdateTaskServiceImpl
 * @Description TODO
 * @Date 9/11/2019 2:31 PM
 * @Created by Rafael
 */
@Slf4j
@Service
public class UpdateTaskServiceImpl implements UpdateTaskService {

    @Autowired
    private IotDeviceMmgClient iotDeviceMmgClient;

    @Override
    public ListResponse<UpgradeTaskVo> getUpgradeTaskListBySite(UpgradeTaskListRequest upgradeTaskListRequest) {
        if(StringUtils.isNotBlank(upgradeTaskListRequest.getTaskId())) {
            try {
                if(Long.valueOf(upgradeTaskListRequest.getTaskId()) == 0) {
                    throw new IllegalArgumentException("请传入正确的升级包编码");
                }
            } catch (Exception e) {
                throw new IllegalArgumentException("请传入正确的升级包编码");
            }
        }
        return iotDeviceMmgClient.getUpgradeTaskListBySite(upgradeTaskListRequest);
    }

    @Override
    public ListResponse<UpgradeTaskDetailVo> getUpgradeTaskDetailListByTaskId(UpgradeTaskDetailRequest upgradeTaskDetailRequest) {
        return iotDeviceMmgClient.getUpgradeTaskDetailListByTaskId(upgradeTaskDetailRequest);
    }

    @Override
    public BaseResponse startTask(UpgradeTaskRequest upgradeTaskRequest) {
        return iotDeviceMmgClient.startTask(upgradeTaskRequest);
    }

    @Override
    public ObjectResponse<UpgradeTaskInfoVo> getUpgradeTaskInfo(UpgradeTaskInfoRequest upgradeTaskInfoRequest) {
        return iotDeviceMmgClient.getUpgradeTaskInfo(upgradeTaskInfoRequest);
    }
}