package com.cdz360.biz.device.business.service;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.device.business.entity.param.ModifyEvseCfgParam;
import com.cdz360.biz.device.business.entity.po.SiteDefultSetting;
import com.cdz360.biz.device.business.entity.request.SiteDefultSettingRequest;
import com.cdz360.biz.device.business.entity.result.SiteDefultSettingVO;
import com.cdz360.biz.device.business.mapper.SiteDefultSettingMapper;
import com.cdz360.biz.device.business.util.DbsAssert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Classname SiteDefultSettingServiceImpl
 * @Description TODO
 * @Date 2019/6/12
 * @Created by wangzheng
 */
@Slf4j
@Service
public class SiteDefultSettingServiceImpl implements SiteDefultSettingService {

    @Autowired
    private SiteDefultSettingMapper siteDefultSettingMapper;
    @Autowired
    private BoxQueryService boxQueryService;
    @Autowired
    private BoxSettingService boxSettingService;

    @Override
    public SiteDefultSettingVO selectBySiteId(String siteId) {
        SiteDefultSettingRequest request = new SiteDefultSettingRequest();
        request.setSiteId(siteId);
        List<SiteDefultSettingVO> list = siteDefultSettingMapper.selectSiteDefultSettingList(request);
        DbsAssert.isTrue(list.size() != 0, "根据siteId=" + siteId + "查询场站默认配置,无记录");
        DbsAssert.isTrue(!list.isEmpty() && list.size() == 1, "根据siteId=" + siteId + "查询场站默认配置结果不唯一");
        return list.get(0);
    }

    @Override
    public Map<String, Object> selectPageSiteDefultSetting(SiteDefultSettingRequest request) {
        Page<SiteDefultSettingVO> pageInfo = PageHelper.startPage(request.getPage(), request.getRows(), true);
        List<SiteDefultSettingVO> list = siteDefultSettingMapper.selectSiteDefultSettingList(request);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("rows", list);
        resultMap.put("total", pageInfo.getTotal());
        return resultMap;
    }

    @Override
    public Integer insertSiteDefultSetting(SiteDefultSettingRequest request) {
        Map<String, Object> columnMap = Maps.newHashMap();
        columnMap.put("siteId", request.getSiteId());
        List<SiteDefultSetting> list = siteDefultSettingMapper.selectByMap(columnMap);
        if (list.size() > 0) {
            throw new DcArgumentException("场站siteId=" + request.getSiteId() + "已存在默认配置");
        }
        SiteDefultSetting siteDefultSetting = new SiteDefultSetting();
        // 请求属性copy到bsBoxSetting
        BeanUtils.copyProperties(request, siteDefultSetting);
        Integer res = siteDefultSettingMapper.insert(siteDefultSetting);
        return res;
    }

    @Override
    public Integer modifySiteDefultSetting(SiteDefultSettingRequest request) {
        log.info("更新场站配置信息: request = {}", request);
        SiteDefultSetting siteDefultSetting = siteDefultSettingMapper.selectById(request.getId());
        DbsAssert.isNotNull(siteDefultSetting, "场站 id=" + request.getId() + "默认配置记录不存在");
        if (StringUtils.isBlank(request.getAdminPassword()) ||
                StringUtils.isBlank(request.getLevel2Password())) {
//                StringUtils.isBlank(request.getUrl())) {
            throw new DcArgumentException("参数不能为空");
        }

        if (request.getIsNoCardCharge()) {
            if (request.getIsScanCharge() ||
                    request.getIsVinCharge() ||
                    request.getIsCardCharge() ||
                    request.getIsQuotaEleCharge() ||
                    request.getIsQuotaMoneyCharge() ||
                    request.getIsQuotaTimeCharge()) {
                throw new DcArgumentException("支持无卡充电时，其他类型与模式不可为‘是’");
            }
        } else {
            if (!request.getIsScanCharge() &&
                    !request.getIsVinCharge() &&
                    !request.getIsCardCharge()) {
                throw new DcArgumentException("不支持无卡充电时，扫码充电、VIN码充电、刷卡充电 中至少有一个为‘是’");
            }
        }
        BeanUtils.copyProperties(request, siteDefultSetting);
        Integer res = siteDefultSettingMapper.updateById(siteDefultSetting);
//        BoxSettingUpsertRequest req = new BoxSettingUpsertRequest();

        // 默认场站配置得桩列表
        List<String> list = boxQueryService.getAllBoxBySiteId(request.getSiteId());
        log.info("evse list size = {}", list.size());
        if (list.isEmpty()) {
            log.info("当前场站下没有默认使用场站配置的桩");
            return res;
        }



        // 配置下发
        ModifyEvseCfgParam param = new ModifyEvseCfgParam();
        BeanUtils.copyProperties(request, param);
        param.setQrUrl(request.getUrl());
        param.setEvseNoList(list);
        this.boxSettingService.downSetting2Evse(param);

        return res;
    }

    @Override
    public Integer deleteSiteDefultSetting(String siteId) {
        Map<String, Object> columnMap = Maps.newHashMap();
        columnMap.put("siteId", siteId);
        Integer res = siteDefultSettingMapper.deleteByMap(columnMap);
        return res;
    }

    @Override
    public SiteDefultSettingVO selectByBoxOutFactoryCode(String boxOutFactoryCode) {
        return siteDefultSettingMapper.selectByBoxOutFactoryCode(boxOutFactoryCode);
    }

}