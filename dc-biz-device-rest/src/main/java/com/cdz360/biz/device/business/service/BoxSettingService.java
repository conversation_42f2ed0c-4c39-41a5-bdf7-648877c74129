package com.cdz360.biz.device.business.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.device.business.entity.param.ModifyEvseCfgParam;
import com.cdz360.biz.device.business.entity.request.BoxSettingListRequest;
import com.cdz360.biz.device.business.entity.request.BoxSettingResultRequest;
import com.cdz360.biz.device.business.entity.request.BoxSettingUpsertRequest;
import com.cdz360.biz.device.business.entity.result.BsBoxSettingBInfoVo;
import com.cdz360.biz.device.business.entity.result.EvsePasscodePo;
import com.cdz360.biz.device.business.entity.result.ListResponseEvseList;
import com.cdz360.biz.device.business.entity.result.UrgencyCardEvse;

import java.util.List;
import java.util.Map;

/**
 * @Classname BoxSettingService
 * @Description 设备配置信息服务
 * @Date 2019/6/6 10:01
 * @Created by JLei
 * @Email <EMAIL>
 */
public interface BoxSettingService {

    public static final int STATUS_KEY_SEND_OK = 1;
    public static final int STATUS_KEY_SEND_FAIL = 2;
    public static final int STATUS_KEY_SENDING = 3;

    /**
     * @param request 分页查询对象列表
     * @return
     */
    Map<String, Object> selectPagedBoxSettingList(BoxSettingListRequest request);

    /**
     * @param request 查询全部对象列表
     * @return
     */
    List<BsBoxSettingBInfoVo> selectAllBoxSettingList(BoxSettingListRequest request);

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    BsBoxSettingBInfoVo selectBoxSettingById(Long id);

    /**
     * @param request 插入对象
     * @return
     */
    Integer insertBoxSetting(BoxSettingUpsertRequest request);

    /**
     * @param request 更新对象
     * @return
     */
    Integer updateBoxSetting(BoxSettingUpsertRequest request);

    Integer updateBoxSettingByEvseId(BoxSettingUpsertRequest request);

    /**
     * @param request 配置下发
     * @return
     */
    void sendBoxSetting(BoxSettingUpsertRequest request);

    /**
     * @param request 批量配置下发
     * @return
     */
    void sendBatchBoxSetting(BoxSettingUpsertRequest request);

    /**
     * @param id 通过主键删除对象
     * @return
     */
    Integer deleteBoxSetting(Long id);


    /**
     * @param ids 批量删除对象
     * @return
     */
    Integer deleteBatchBoxSetting(List<Long> ids);

    /**
     * @param request 根据token获取相关的配置结果列表
     * @return
     */
    ListResponse<BsBoxSettingBInfoVo> getResultByCommIds(String commIdChain, BoxSettingResultRequest request);

    /**
     * 根据token获取商户下子商户的所有桩后下发二维码修改的配置
     *
     * @param
     * @param qrCode
     * @return
     */
    Integer updateBoxSettingQRCode(List<Long> ids, String qrCode);

    /**
     * 再次下发
     *
     * @param token
     * @param id
     * @return
     */
    Integer sendQRCodeById(String token, Long id);

    /**
     * 失败再次下发
     *
     * @param token
     * @return
     */
    Integer sendFailQRCodeByToken(String token, Long commId);

    /**
     * 批量下发紧急充电卡
     *
     * @param request
     * @return Map(场站ID, 离线桩号列表)
     */
    Map<String, Object> sendBatchWhiteCards(List<BoxSettingListRequest> request);

    /**
     * 更新紧急充电卡-桩关系表
     *
     * @param evseId
     * @param status 2-成功，1-失败
     * @return
     */
    int updateStatusOnEvseIdAndStatus(String evseId, int status);

    /**
     * 下发紧急充电卡
     *
     * @param request
     * @return 该场站下离线桩号列表
     */
    List<String> sendWhiteCard(BoxSettingListRequest request);


    /**
     * 根据下发状态进行Count
     *
     * @param stationCode     必传，站点编号
     * @param whiteCardStatus 可传，紧急充电卡下发状态1下发成功2失败3下发中
     * @return
     */
    Integer selectCountByStationCodeAndWhiteCardStatus(String stationCode, Integer whiteCardStatus);

    /**
     * 根据stationIds获取相关桩的信息，[,]分割
     *
     * @param stationIds
     */
    ListResponseEvseList<UrgencyCardEvse> urgencyCardsDetailEvseList(String stationIds, String cardChipNo, String evse, int page, int pageSize);


    ObjectResponse<EvsePasscodePo> getEvsePasscode(String evseNo);

    /**
     * 给桩下发场站默认配置信息
     *
     * @param evseNo 桩编号
     * @param siteId 场站Id
     * @return
     */
    void downSetting2Evse(String evseNo, String siteId);

    void downSetting2Evse(ModifyEvseCfgParam param);
}
