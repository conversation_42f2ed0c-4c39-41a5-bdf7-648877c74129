package com.cdz360.biz.device.business.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.device.business.entity.request.TemplateListRequest;
import com.cdz360.biz.device.business.entity.result.BoxInfoVo;
import com.cdz360.biz.device.business.entity.result.TemplateFullInfoVo;
import com.cdz360.biz.device.business.entity.result.TemplateInfoVo;

/**
 * <p>
 * 计费模板**主模板** 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-21
 */
public interface TemplateService {

    /**
     * 分页获取计费模板列表
     * 接口逻辑有误
     * 由getTemplateInfoList方法代替
     *
     * @param request
     * @return
     * @throws DcServiceException
     */
    ListResponse<TemplateInfoVo> getPagedTemplateInfoList(TemplateListRequest request) throws DcServiceException;

    /**
     * 分页获取计费模板列表
     *
     * @param request
     * @return
     * @throws DcServiceException
     */
    ListResponse<TemplateInfoVo> getTemplateInfoList(TemplateListRequest request//, String token
    ) throws DcServiceException;


    /**
     * 根据ID获取计费模板详情
     *
     * @param templateId
     * @return
     * @throws DcServiceException
     */
    TemplateInfoVo getTemplateDetailById(Long templateId) throws DcServiceException;

    /**
     * 根据ID获取计费模板
     *
     * @param templateId
     */
    TemplateInfoVo getTemplateById(Long templateId) throws DcServiceException;

    /**
     * 根据计费模板编号，获取最新的计费模板详情
     *
     * @param code
     * @return
     * @throws DcServiceException
     */
    TemplateInfoVo getLatestTemplateDetailByCode(String code) throws DcServiceException;

    /**
     * 根据枪头，获取设备关联的计费模板详情
     *
     * @param evseNo
     * @return
     * @throws DcServiceException
     */
    TemplateInfoVo getTemplateDetailByBcId(String evseNo) throws DcServiceException;


    /**
     * 根据设备信息下发计费模板和紧急卡或者通用配置
     *
     * @param templateInfoVo
     * @param boxInfoVo
     * @return
     */
    boolean notifyUpdateDeviceTemplateOrSettingByDevice(TemplateInfoVo templateInfoVo, BoxInfoVo boxInfoVo);

//    /**
//     * 根据设备信息下发计费模板
//     * 批量下发
//     *
//     * @param templateInfoVo
//     * @return
//     */
//    boolean notifyUpdateDeviceTemplateByDeviceList(TemplateInfoVo templateInfoVo, List<BoxInfoVo> boxInfoVoList);
//

    /**
     * 根据ID获取计费模板详情(包括当前价格)
     *
     * @param evseNo
     * @return
     * @throws DcServiceException
     */
    TemplateFullInfoVo getFullTemplateDetailByBcId(String evseNo);

    /**
     * 计费模板启用/禁用
     *
     * @param id
     * @param enable
     */
    void enable(Long id, Boolean enable);
}
