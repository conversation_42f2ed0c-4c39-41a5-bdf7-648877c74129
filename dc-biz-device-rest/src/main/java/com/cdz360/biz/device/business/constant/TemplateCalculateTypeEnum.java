package com.cdz360.biz.device.business.constant;

import lombok.Getter;

/**
 * 计费模板的计费模式
 *
 * <AUTHOR>
 * @date Create on 2018/11/2 16:01
 */
@Getter
public enum TemplateCalculateTypeEnum {

    /**
     * 所有功率统一计费
     */
    TYPE_POWER_UNIFIED(10),
    /**
     * 按不同功率段分别计费
     */
    TYPE_POWER_RANGE(11),
    /**
     * 所有时段统一计费
     */
    TYPE_TIME_UNIFIED(20),
    /**
     * 电费按不同时段分别计费
     */
    TYPE_TIME_RANGE(21),
    /**
     * 电费服务费按不同时段分别计费
     */
    TYPE_TIME_RANGE_SEVR_FEE(22),
    //
    ;
    private int code;

    TemplateCalculateTypeEnum(int code) {
        this.code = code;
    }

    /**
     * 根据value值获取对应的枚举
     *
     * @param code
     * @return
     */
    public static TemplateCalculateTypeEnum getEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TemplateCalculateTypeEnum typeEnum : TemplateCalculateTypeEnum.values()) {
            if (code == typeEnum.code) {
                return typeEnum;
            }
        }
        return null;
    }
}
