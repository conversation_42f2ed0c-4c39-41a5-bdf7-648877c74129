package com.cdz360.biz.device.business.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskDetailRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskInfoRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskListRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskRequest;
import com.cdz360.biz.device.business.entity.result.UpgradeTaskDetailVo;
import com.cdz360.biz.device.business.entity.result.UpgradeTaskInfoVo;
import com.cdz360.biz.device.business.entity.result.UpgradeTaskVo;

/**
 * @Classname UpdateTaskService
 * @Description TODO
 * @Date 9/11/2019 2:30 PM
 * @Created by Rafael
 */
public interface UpdateTaskService {

    ListResponse<UpgradeTaskVo> getUpgradeTaskListBySite(UpgradeTaskListRequest upgradeTaskListRequest);

    ListResponse<UpgradeTaskDetailVo> getUpgradeTaskDetailListByTaskId(UpgradeTaskDetailRequest upgradeTaskDetailRequest);

    BaseResponse startTask(UpgradeTaskRequest upgradeTaskRequest);

    ObjectResponse<UpgradeTaskInfoVo> getUpgradeTaskInfo(UpgradeTaskInfoRequest upgradeTaskInfoRequest);

}