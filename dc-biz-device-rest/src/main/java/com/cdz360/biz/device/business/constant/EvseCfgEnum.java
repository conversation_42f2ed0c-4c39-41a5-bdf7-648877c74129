package com.cdz360.biz.device.business.constant;

/**
 * @Classname EvseCfgEnum
 * @Description 桩配置下发枚举
 * @Date 2019/6/27 9:15
 * @Created by JLei
 * @Email <EMAIL>
 */
public enum EvseCfgEnum {

    /**
     * 桩配置下发成功
     */
    EVSE_CFG_SEND_SUCC(1),
    /**
     * 桩配置下发失败
     */
    EVSE_CFG_SEND_FAIL(2),
    /**
     * 桩配置下发过程中
     */
    EVSE_CFG_SEND_PROCESS(3),
    //
    ;
    public int value;

    EvseCfgEnum(int value) {
        this.value = value;
    }

    /**
     * 根据value值获取对应的枚举
     *
     * @param value
     * @return
     */
    public static EvseCfgEnum getEnumByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (EvseCfgEnum typeEnum : EvseCfgEnum.values()) {
            if (value == typeEnum.value) {
                return typeEnum;
            }
        }
        return null;
    }

}
