package com.cdz360.biz.model.cus.commScore.vo;

import com.cdz360.biz.model.cus.commScore.po.CommScoreLevelPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CommScoreVo {

    @Schema(description = "商户会员ID")
    private Long commCusId;

    @Schema(description = "商户会员积点")
    private BigDecimal score;

    @Schema(description = "对应等级")
    private Integer level;

    @Schema(description = "等级规则")
    private List<CommScoreLevelPo> levelList;

}
