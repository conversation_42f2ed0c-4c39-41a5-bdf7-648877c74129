package com.cdz360.biz.model.cus.message.vo;

import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
public class MessageVo {

    private Long id;
    private String title;
    private String content;
    private Long msgType;
    private Long platform;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    private Boolean enable;
    private String commNameList;
    private String username;
    private String corpName;

}
