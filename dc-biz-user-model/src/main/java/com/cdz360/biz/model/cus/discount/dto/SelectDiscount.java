package com.cdz360.biz.model.cus.discount.dto;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.cus.discount.type.DiscountStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "查询协议价参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SelectDiscount extends BaseObject {
    @Schema(description = "绑定账号ID(t_user.id)")
    @NotNull(message = "uid 不能为 null")
    private Long uid;

    @Schema(description = "帐户类型: 0(未知); 1(个人帐户); 2(授信帐户); 3(商户会员); 4(即充即退); 5(企业客户)")
    @NotNull(message = "accountType 不能为 null")
    private PayAccountType accountType;

    @Schema(description = "账户编号: 商户会员时为所属商户的商户ID，其他账户时为集团商户ID")
    @NotNull(message = "accountCode 不能为 null")
    private Long accountCode;

//    @Schema(description = "站点编号")
//    @NotNull(message = "siteId 不能为 null")
//    @Size(max = 32, message = "siteId 长度不能超过 32")
//    private String siteId;

    @Schema(description = "状态值")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<DiscountStatus> statusList;
}
