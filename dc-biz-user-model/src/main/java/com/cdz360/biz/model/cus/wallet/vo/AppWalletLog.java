package com.cdz360.biz.model.cus.wallet.vo;

import com.cdz360.biz.model.cus.wallet.type.AppWalletLogType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "小程序端显示的钱包流水")
public class AppWalletLog {

    @Schema(description = "流水ID", example = "123456")
    private Long id;

    @Schema(description = "流水类型，0-未知，1-待支付，2-充值，3-支付订单，4-提现", example = "2")
    private AppWalletLogType logType;

    @Schema(description = "流水类型名称（和logType对应，作为入参时无需传递）", example = "提现")
    private String logTypeName;

    @Schema(description = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", locale = "zh", timezone = "GMT+8")
    private Date time;

    @Schema(description = "描述信息")
    private String desc;


    @Schema(description = "金额, 单位'元', 2位小数")
    private BigDecimal amount;

    @Schema(description = "订单号")
    private String orderNo;
}
