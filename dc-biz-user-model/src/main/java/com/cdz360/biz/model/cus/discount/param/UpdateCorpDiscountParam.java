package com.cdz360.biz.model.cus.discount.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.model.cus.discount.dto.SiteDiscount;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "更新企业客户协议价参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UpdateCorpDiscountParam extends BaseObject {
    @Schema(description = "t_corp的id", required = true)
    @NotNull(message = "企业客户ID(corpId)不能为空")
    private Long corpId;

    @Schema(description = "生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date activeDate;

    @Schema(description = "结算类型: UNKNOWN(0)-未知,BALANCE(1)-账户余额扣减(先付费),GUARANTEE(2)-担保消费结算," +
            "POSTPAID(3)-预消费结算(后付费),PARTNER(4)-外部平台结算", required = true, format = "java.lang.Integer")
    @NotNull(message = "企业客户结算类型(settlementType)不能为空")
    private SettlementType settlementType;

    @Schema(description = "是否设置协议价", required = false)
//    @NotNull(message = "是否设置协议价(discountSwitch)不能为空")
    private Boolean discountSwitch;

    @Schema(description = "场站协议价配置列表")
    private List<SiteDiscount> siteDiscountList;

    @Schema(description = "操作人ID")
    private Long updateOpId;

    @Schema(description = "操作人名字")
    @Size(max = 32, message = "updateOpName 长度不能超过 32")
    private String updateOpName;

    /**
     * 校验参数
     *
     * @param param
     */
    public static void checkValue(UpdateCorpDiscountParam param) {
        if (null == param) {
            throw new DcArgumentException("参数无效");
        }

        if (null == param.getCorpId()) {
            throw new DcArgumentException("请提供企业ID");
        }

        if (param.getSettlementType() == null) {
            throw new DcArgumentException("需要指定结算类型");
        }

        if (SettlementType.BALANCE.equals(param.getSettlementType())) {
            if (null == param.getDiscountSwitch()) {
                throw new DcArgumentException("是否设置协议价(discountSwitch)不能为空");
            }

            if (param.getDiscountSwitch() &&
                    CollectionUtils.isEmpty(param.getSiteDiscountList())) {
                throw new DcArgumentException("场站协议价配置不能为空");
            }
        }
    }
}
