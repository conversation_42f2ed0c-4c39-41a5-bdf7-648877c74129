package com.cdz360.biz.model.cus.balance.vo;

import com.cdz360.biz.model.cus.balance.po.BalanceApplicationPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * BalanceApplicationVo
 *
 * @since 7/5/2021 5:26 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper=false)
@Schema(description = "账户金额变更申请-审核")
public class BalanceApplicationVo extends BalanceApplicationPo {
    private String applierName;

    // 所属商户名称
    private String commName;

    private Date payTime;

    // 摘要
    private String corpDigest;
}