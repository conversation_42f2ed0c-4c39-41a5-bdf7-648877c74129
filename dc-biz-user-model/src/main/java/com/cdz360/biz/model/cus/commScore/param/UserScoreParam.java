package com.cdz360.biz.model.cus.commScore.param;

import com.cdz360.biz.model.cus.commScore.type.ResetType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商户会员积点
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class UserScoreParam {
   /**
    * 商户ID
    */
   private Long commId;
   /**
    * 商户会员ID
    */
   private Long cusId;
   /**
    * 积点数
    */
   private BigDecimal score;
   /**
    * 操作类型
    */
   private ResetType type;
   /**
    * 操作人员
    */
   private Long sysUid;
}