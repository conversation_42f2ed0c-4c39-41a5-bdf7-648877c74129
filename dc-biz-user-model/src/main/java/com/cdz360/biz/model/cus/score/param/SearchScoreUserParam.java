package com.cdz360.biz.model.cus.score.param;

import com.cdz360.base.model.base.param.BaseListParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * SearchScoreLogParam
 * 
 * @since 1/5/2023 4:58 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SearchScoreUserParam extends BaseListParam {
    @Schema(description = "积分ID")
    private Long scoreSettingId;

    @Schema(description = "关键字")
    private String keyword;
}