package com.cdz360.biz.model.cus.score.dto;

import com.cdz360.biz.model.cus.score.po.ScoreSettingLevelPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * UserScoreSettingLevelDto
 * 
 * @since 7/27/2023 1:10 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UserScoreSettingLevelDto extends ScoreSettingLevelPo {

    private Long userId;

    @ApiModelProperty(value = "用户当前积分")
    private Long score;

    @Schema(description = "符合积分等级信息")
    @JsonInclude(Include.NON_NULL)
    private ScoreSettingLevelDto levelDto;
}