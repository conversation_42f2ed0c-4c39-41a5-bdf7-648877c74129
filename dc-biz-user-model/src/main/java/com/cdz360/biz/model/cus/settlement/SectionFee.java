package com.cdz360.biz.model.cus.settlement;

import com.cdz360.biz.model.discount.vo.Fee;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "阶梯式单价/阶梯式分段")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SectionFee extends Fee {
    @Schema(description = "开始电量")
    private BigDecimal fromKwh;

    @Schema(description = "结束电量")
    private BigDecimal toKwh;
}
