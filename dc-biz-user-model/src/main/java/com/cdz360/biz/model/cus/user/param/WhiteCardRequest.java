package com.cdz360.biz.model.cus.user.param;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * WhiteCardRequest
 *  紧急充电卡请求
 * @since 2019/7/15 9:15
 * <AUTHOR>
 *    <EMAIL>
 */
@Data
@Accessors(chain = true)
public class WhiteCardRequest {
    // 物理卡号
    private String cardChipNo;
    // 要弃用的物理卡号列表
    private List<String> cardChipNoList;
    //场站id
    private String site;
    //场站列表
    private List<String> siteList;
    // 是否弃用
    private Boolean isAbandon;
    // 是否重置密码
    private Boolean isResetPass;
    // 登录token令牌
    private String token;
}