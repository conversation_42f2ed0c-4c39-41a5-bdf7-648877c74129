package com.cdz360.biz.model.cus.settlement.dto;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.cus.settlement.po.SettlementCfgPo;
import com.cdz360.biz.model.cus.settlement.po.SettlementPo;
import com.cdz360.biz.model.cus.settlement.type.SubSettlementType;
import com.cdz360.biz.model.order.param.ListSettlementOrderParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "账单操作")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SettlementDto extends SettlementPo {
    public enum OpPage {
        ADD,
        EDIT
    }

    @Schema(description = "添加账单页面/编辑页面: ADD -- 添加账单页面; EDIT -- 编辑账单页面", required = true)
    private OpPage opPage;

    @Schema(description = "是否全选")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean selectAll;

    @Schema(description = "查询账单关联订单的参数 全选必传")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ListSettlementOrderParam orderParam;

    @Schema(description = "变更充电订单列表 非全选的情况必填")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> orderNoList;

//    @Schema(description = "是否是结算(区分编辑页面的保存和结算使用): true(编辑页面)")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Boolean settlement;

    @Schema(description = "企业名称 账单生成的时候使用", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String corpName;

    @Schema(description = "对应的结算方案的配置", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SettlementCfgPo settlementCfgPo;

    public static void appendCheckValue(SettlementDto param) {
        checkValue(param);

        if (null == param.getOpPage()) {
            throw new DcArgumentException("操作页面需要提供");
        }

        // 需要区分追加/删除
        if (null != param.getSelectAll() && param.getSelectAll() && null == param.getOrderParam()) {
            throw new DcArgumentException("全选需要提供查询订单的参数");
        } else if ((null == param.getSelectAll() || !param.getSelectAll())
                && CollectionUtils.isEmpty(param.getOrderNoList())) {
            throw new DcArgumentException("需要提供本次操作的订单号列表");
        }
    }

    public static void removeCheckValue(SettlementDto param) {
        checkValue(param);

        if ( StringUtils.isBlank(param.getBillNo())) {
            throw new DcArgumentException("账单编号不能为空");
        }

//        if (null == param.getSettlementType()) {
//            throw new DcArgumentException("需要提供结算类型");
//        }

    }

    /**
     * 校验参数
     *
     * @param param
     * @return
     */
    private static void checkValue(SettlementDto param) {
        if (null == param) {
            throw new DcArgumentException("参数无效");
        }

        if (null == param.getCorpId()) {
            throw new DcArgumentException("企业ID不能为空");
        }

        if (null == param.getCfgId()) {
            throw new DcArgumentException("结算方案配置ID不能为空");
        }

        // 结算方案必填
        if (param.getSubSettlementType() == null || param.getSubSettlementType() == SubSettlementType.UNKNOWN) {
            throw new DcArgumentException("企业结算方案无效");
        }

        // 账单名称
        if (StringUtils.isBlank(param.getBillName())) {
            throw new DcArgumentException("账单名称不能为空");
        }

        // check many parameter...
    }

    public void updateOrderData(SettlementDto dto) {
        this.setOrderNum(Optional.ofNullable(dto.getOrderNum()).orElse(0));
        this.setOrderKwh(Optional.ofNullable(dto.getOrderKwh()).orElse(BigDecimal.ZERO));
        this.setKwhOther(Optional.ofNullable(dto.getKwhOther()).orElse(BigDecimal.ZERO));
        this.setKwhJian(Optional.ofNullable(dto.getKwhJian()).orElse(BigDecimal.ZERO));
        this.setKwhFeng(Optional.ofNullable(dto.getKwhFeng()).orElse(BigDecimal.ZERO));
        this.setKwhPing(Optional.ofNullable(dto.getKwhPing()).orElse(BigDecimal.ZERO));
        this.setKwhGu(Optional.ofNullable(dto.getKwhGu()).orElse(BigDecimal.ZERO));
        this.setOrderElecFee(Optional.ofNullable(dto.getOrderElecFee()).orElse(BigDecimal.ZERO));
        this.setOrderServFee(Optional.ofNullable(dto.getOrderServFee()).orElse(BigDecimal.ZERO));
        this.setOrderElecProfit(Optional.ofNullable(dto.getOrderElecProfit()).orElse(BigDecimal.ZERO));
        this.setOrderServProfit(Optional.ofNullable(dto.getOrderServProfit()).orElse(BigDecimal.ZERO));
        this.setOrderTotalProfit(Optional.ofNullable(dto.getOrderTotalProfit()).orElse(BigDecimal.ZERO));
        this.setSiteNameList(dto.getSiteNameList());
        this.setSiteNoList(dto.getSiteNoList());
    }

//    public void clearData() {
//        // 保底信息需要根据实际调整
////        this.setGuaranteeKwh(BigDecimal.ZERO);
////        this.setGuaranteeElecFee(BigDecimal.ZERO);
////        this.setGuaranteeServFee(BigDecimal.ZERO);
////        this.setSubSettlementType(null);
////        this.setSubSettlement(null);
//
//        // 账单相关信息需要根据保底来计算
////        this.setSettlementTotalFee(BigDecimal.ZERO);
////        this.setSettlementServFee(BigDecimal.ZERO);
////        this.setSettlementElecFee(BigDecimal.ZERO);
//
//        // 充电订单信息是0
//        this.setOrderNum(0);
//        this.setOrderKwh(BigDecimal.ZERO);
//        this.setKwhOther(BigDecimal.ZERO);
//        this.setKwhJian(BigDecimal.ZERO);
//        this.setKwhFeng(BigDecimal.ZERO);
//        this.setKwhPing(BigDecimal.ZERO);
//        this.setKwhGu(BigDecimal.ZERO);
//        this.setOrderElecFee(BigDecimal.ZERO);
//        this.setOrderServFee(BigDecimal.ZERO);
//        this.setSiteNameList(null);
//        this.setSiteNoList(null);
//    }
}
