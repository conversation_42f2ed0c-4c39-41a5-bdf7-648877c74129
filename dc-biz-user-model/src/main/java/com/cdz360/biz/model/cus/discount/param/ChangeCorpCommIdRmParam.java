package com.cdz360.biz.model.cus.discount.param;

import com.cdz360.biz.model.cus.site.vo.SiteSimpleVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "变更企业客户所属商户删除参数")
public class ChangeCorpCommIdRmParam {

    @Schema(description = "企业客户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    @Schema(description = "需要移除的场站列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SiteSimpleVo> removeList;
}
