package com.cdz360.biz.model.cus.commScore.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CommScoreLevelPo {

    private Long id;

    @Schema(description = "商户ID")
    private Long commId;

    @Schema(description = "会员等级")
    private Integer level;

    @Schema(description = "最小积点")
    private Long minScore;

    @Schema(description = "最大积点")
    private Long maxScore;

    @Schema(description = "服务费折扣")
    private BigDecimal discount;

    @Schema(description = "是否可用")
    private Boolean enable;

}
