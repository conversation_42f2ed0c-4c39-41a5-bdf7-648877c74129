package com.cdz360.biz.model.cus.wallet.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "银行卡信息")
public class BankCardVo  implements Serializable {
    private static final long serialVersionUID = -4762526613126998545L;

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long cusId;

    @Schema(description = "卡号")
    private String cardNo;

    @Schema(description = "持卡人姓名")
    private String ownerName;

    @Schema(description = "发卡行名称")
    private String bankName;

    @Schema(description = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "修改日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Schema(description = "是否有效")
    private Boolean enable;

}
