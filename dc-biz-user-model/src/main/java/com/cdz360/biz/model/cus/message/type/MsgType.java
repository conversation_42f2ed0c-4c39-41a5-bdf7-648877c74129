package com.cdz360.biz.model.cus.message.type;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

/**
 * 消息类型
 * <AUTHOR>
 */

@Getter
public enum MsgType implements DcEnum {
        UNKNOWN(0, "未知"),
        NOTICE(1, "升级通知" ),
        ANNOUNCEMENT(2, "平台公告"),
        REMIND(3, "续费提醒"),
        OTHER(4, "其他消息"),
        INSPECTION_REMINDER(5, "巡检提醒"),
        YW_REMINDER(6, "运维提醒"),
        ;

        private int code;
        private String desc;


    MsgType(int i, String s) {
        this.code = i;
        this.desc = s;
    }
}
