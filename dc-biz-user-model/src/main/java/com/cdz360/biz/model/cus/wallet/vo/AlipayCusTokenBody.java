package com.cdz360.biz.model.cus.wallet.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "支付宝用户Token信息")
public class AlipayCusTokenBody {

    @JsonProperty("alipay_system_oauth_token_response")
    @Schema(description = "alipay_system_oauth_token_response")
    private AlipayCusToken alipaySystemOauthTokenResponse;

    @JsonProperty("alipay_cert_sn")
    @Schema(description = "alipay_cert_sn")
    private String alipayCertSn;

    @JsonProperty("sign")
    @Schema(description = "sign")
    private String sign;
}
