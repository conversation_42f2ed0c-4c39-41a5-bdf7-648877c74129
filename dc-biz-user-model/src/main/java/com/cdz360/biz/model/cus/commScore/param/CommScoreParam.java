package com.cdz360.biz.model.cus.commScore.param;

import com.cdz360.biz.model.cus.commScore.po.CommScoreLevelPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * 商户会员积点
 */
@Data
public class CommScoreParam {
    /**
     * 商户ID
     */
    @Schema(description = "商户ID")
    private Long commId;

    @Schema(description = "商户名称")
    private String commName;

    @Schema(description = "是否开启商户等级 true-开启")
    private Boolean enableUseScore;

    private List<CommScoreLevelPo> levelList;
}