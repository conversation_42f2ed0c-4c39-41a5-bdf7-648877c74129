package com.cdz360.biz.model.cus.settlement.po;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.cus.settlement.type.SettlementCfgStatusEnum;
import com.cdz360.biz.model.cus.settlement.type.SubSettlementType;
import com.cdz360.biz.model.settle.vo.BillConfig;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "企业结算方式配置")
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SettlementCfgPo extends BaseObject {
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "t_corp的id", required = true)
    private Long corpId;

    @Schema(description = "状态: UNKNOWN, ACTIVE, INACTIVE, EXPIRED")
    private SettlementCfgStatusEnum status;

    @Schema(description = "生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date activeDate;

    @Schema(description = "是否有保底消费, true/false")
    private Boolean guarantee;

    @Schema(description = "保底电量, <= 0 表示无保底电量要求: 单位(kW·h)")
    private Integer guaranteeKwh;

    @Schema(description = "保底电费单价: 单位(元/kW·h)")
    private BigDecimal guaranteeElecFee;

    @Schema(description = "保底服务费单价: 单位(元/kW·h)")
    private BigDecimal guaranteeServFee;

    @Schema(description = "价格方案: 0, 未知; 11, 固定价格; 12, 订单原价; 13, 自定义尖峰平谷; 14, 阶梯式单价; 15, 阶梯式分段",
            format = "java.lang.Integer")
    private SubSettlementType subSettlementType;

    @Schema(description = "结算方案")
    private String subSettlement;

    @Schema(description = "账单自动生成日期 1 -- 28")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Integer settlementDate;

    @Schema(description = "首次账单生成年月", example = "2022-10")
    @JsonFormat(pattern = "yyyy-MM", locale = "zh", timezone = "GMT+8")
    private Date firstSettDate;

    @Schema(description = "账单自动生成配置")
    private List<BillConfig> billConfigList;

    @Schema(description = "是否有效. true/false")
    private Boolean enable;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;
}
