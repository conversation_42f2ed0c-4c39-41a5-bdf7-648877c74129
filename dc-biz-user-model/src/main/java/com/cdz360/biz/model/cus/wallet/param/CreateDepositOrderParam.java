package com.cdz360.biz.model.cus.wallet.param;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 创建支付订单参数
 */
@Data
@Accessors(chain = true)
@Schema(description = "创建支付订单参数")
public class CreateDepositOrderParam {

    @Schema(description = "客户ID", required = true)
    private long cusId;

    @Schema(description = "支付方式. 微信/支付宝", required = true)
    private PayChannel payChannel;

    @Schema(description = "账户类型", required = true)
    private PayAccountType accountType;

    @Schema(description = "账户ID", required = true)
    private long accountId;

    @Schema(description = "商户ID(商户会员充值时必传)")
    private Long commId;

    @Schema(description = "集团商户ID", required = true)
    private long topCommId;

    @Schema(description = "充值金额, 单位'元'", required = true)
    private BigDecimal amount;

    @Schema(description = "客户端类型", required = true)
    private AppClientType appClientType;

    @Schema(description = "微信openid/支付宝buyer_id", required = true)
    private String openid;

    @Schema(description = "微信/支付宝小程序formId. 用于发送微信/支付宝推送消息")
    private String formId;

    @Schema(description = "客户端IP")
    private String clientIp;

    @Schema(description = "备注")
    private String remark;


    @Schema(description = "充电订单号 目前用来即充即退充电缴费使用")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String chargeOrderNo;

    @Schema(description = "占位费订单号 目前用来即充即退充电占位费缴费使用")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String overtimeParkOrderNo;

    @Schema(description = "微信登录session")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String sessionKey;

    @Schema(description = "微信登录加密参数")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String encryptedData;

    @Schema(description = "微信登录加密参数")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String iv;

    @Schema(description = "数币账户 授权码/手机号(现阶段使用手机号)", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String digiccyAccount;

    @Schema(description = "功能订阅充值单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String payNo;

//    @Schema(description = "是否使用数字货币")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Boolean useDigiccy;
}
