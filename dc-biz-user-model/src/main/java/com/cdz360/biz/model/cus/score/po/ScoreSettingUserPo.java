package com.cdz360.biz.model.cus.score.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "积分用户")
public class ScoreSettingUserPo {

	@Schema(description = "主键ID")
	private Long id;

	@ApiModelProperty(value = "顶级商户ID")
	private Long topCommId;

	@Schema(description = "积分体系ID")
	private Long scoreSettingId;

	@Schema(description = "手机号")
	private String phone;

	@Schema(description = "1-包含用户，0-不包含用户")
	private Boolean enable;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

}
