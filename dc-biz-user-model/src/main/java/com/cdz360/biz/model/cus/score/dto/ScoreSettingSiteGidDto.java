package com.cdz360.biz.model.cus.score.dto;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.biz.model.cus.score.po.ScoreSettingPo;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * ScoreSettingSiteGidDto
 *
 * @since 7/27/2023 11:27 AM
 * <AUTHOR>
 */@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ScoreSettingSiteGidDto extends ScoreSettingPo {
    private String gid;

    // 积分体系可用的支付类型
    private List<PayAccountType> supportPayAccountType = List.of(
        PayAccountType.PERSONAL,
        PayAccountType.PREPAY,
        PayAccountType.WX_CREDIT,
        PayAccountType.ALIPAY_CREDIT,
        PayAccountType.COMMERCIAL
    );
}