package com.cdz360.biz.model.cus.message.type;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

/**
 * 归属平台
 * <AUTHOR>
 */
@Getter
public enum PlatformType implements DcEnum {
    UNKNOWN(0, "未知"),
    CORP(22, "企业平台" ),
    MANAGE(20, "管理平台&桩管家"),
    HW(30,"海外版充电管理平台"),
    ALL(99, "管理平台&桩管家&企业平台"),
            ;

    private int code;
    private String desc;


    PlatformType(int i, String s) {
        this.code = i;
        this.desc = s;
    }
}
