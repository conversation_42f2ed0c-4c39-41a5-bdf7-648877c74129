package com.cdz360.biz.model.cus.SiteAuthVin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "VIN本地认证场站列表")
public class SiteListVo {

	/**
	 * 场站ID
	 */
	private String siteId;

	/**
	 * 场站名称
	 */
	private String siteName;

	/**
	 * 已生效数
	 */
	private Long enableAmount;

	/**
	 * 支持桩数
	 */
	private Integer amount = 0;

}
