package com.cdz360.biz.model.cus.settlement.param;

import com.cdz360.base.model.base.vo.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "企业客户账单后付费统计账单数据")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UpdateSettlementInvoicedAmountParam extends BaseObject {
    public enum OpType {
        FIXED,
        ROLL_BACK
    }

    @Schema(description = "操作方式")
    private OpType opType;

    @Schema(description = "开票申请单号")
    private String applyNo;

    @Schema(description = "oa流程实例ID")
    private String procInstId;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "商户ID链", example = "34474,34475", hidden = true)
    private String commIdChain;

    @Schema(description = "企业客户Id", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NotNull
    private Long corpId;

    @Schema(description = "账单编号列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @NotEmpty
    private List<String> billNoList;
}
