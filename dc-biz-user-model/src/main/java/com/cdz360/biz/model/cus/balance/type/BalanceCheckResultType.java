package com.cdz360.biz.model.cus.balance.type;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

/**
 * BalanceCheckResultType
 *
 * @since 6/25/2021 9:50 AM
 * <AUTHOR>
 */
@Getter
public enum BalanceCheckResultType implements DcEnum {
    ALLOW(0, "通过"),
    DENY(1, "不通过"),
    ;

    private final int code;
    private final String desc;

    BalanceCheckResultType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BalanceCheckResultType valueOf(int code) {
        for (BalanceCheckResultType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return BalanceCheckResultType.ALLOW;
    }
}
