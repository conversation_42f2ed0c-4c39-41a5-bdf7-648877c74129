package com.cdz360.biz.model.cus.score.param;

import com.cdz360.biz.model.cus.commScore.type.ResetType;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * ScoreUpdateParam
 * 
 * @since 1/9/2023 2:06 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper=false)
public class ScoreUpdateParam {

    private Long scoreSettingId;

    private Long sysUid;

    private BigDecimal score;

    private String orderNo;

    private Long userId;

    private ResetType type;

    private BigDecimal scoreLatest;
}