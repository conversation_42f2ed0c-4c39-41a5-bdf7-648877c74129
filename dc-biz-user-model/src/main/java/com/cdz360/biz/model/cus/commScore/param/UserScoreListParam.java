package com.cdz360.biz.model.cus.commScore.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.cus.commScore.type.ResetType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 商户会员积点列表
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class UserScoreListParam extends BaseListParam {
   /**
    * 商户ID
    */
   private Long commId;
   /**
    * 商户会员ID
    */
   private Long cusId;
   /**
    * 开始时间
    */
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
   private Date startTime;
   /**
    * 结束时间
    */
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
   private Date endTime;
}