package com.cdz360.biz.model.cus.sysUser.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "操作日志")
public class OpLogPo {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "商户ID")
    private Long commId;

    @Schema(description = "应用类型")
    private Integer appType;

    @Schema(description = "账号ID")
    private Long opUid;

    @Schema(description = "操作人账号")
    private String opUsername;

    @Schema(description = "操作人名字")
    private String opName;

    @Schema(description = "操作标签")
    private String tag;

    @Schema(description = "操作详情")
    private String info;

    @Schema(description = "企业ID.")
    private Long corpId;

    @Schema(description = "企业组织ID.")
    private Long corpOrgId;

    @Schema(description = "请求参数")
    private String param;

    @Schema(description = "关联日志的traceId")
    private String traceId;

    @Schema(description = "客户端公网IP")
    private String clientIpA;

    @Schema(description = "客户端内网IP")
    private String clientIpB;

    @Schema(description = "操作接口类名")
    private String clazzName;

    @Schema(description = "操作函数名")
    private String methodName;

    @Schema(description = "接口URL地址")
    private String methodUrl;

    @Schema(description = "创建时间")
    private Date createTime;


}
