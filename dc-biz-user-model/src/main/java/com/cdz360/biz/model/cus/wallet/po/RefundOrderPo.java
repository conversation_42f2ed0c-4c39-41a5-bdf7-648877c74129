package com.cdz360.biz.model.cus.wallet.po;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.biz.model.common.po.BasePo;
import com.cdz360.biz.model.cus.wallet.type.RefundStatus;
import com.cdz360.biz.model.finance.type.FlowInAccountType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RefundOrderPo extends BasePo {

    @Schema(description = "客户ID")
    private long cusId;

    @Schema(description = "提现来源(应用)")
    private AppClientType appType;

    @Schema(description = "银行卡ID")
    private long bankCardId;

    @Schema(description = "银行卡号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String bankCardNo;

    @Schema(description = "提现金额, 单位: 元", example = "1234.56")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amount;

    @Schema(description = "退款的余额, 单位: 元", example = "1234.56")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal balance;

    @Schema(description = "提现前余额, 单位'元', 2位小数. 含赠送余额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amountBefore;

    @Schema(description = "提现前实际余额(成本), 单位'元', 2位小数.")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal costBefore;

    @Schema(description = "提现后余额, 单位'元', 2位小数. 含赠送余额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amountAfter;

    @Schema(description = "状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private RefundStatus status;

    @Schema(description = "集团商户ID")
    private long topCommId;

    @Schema(description = "商户ID")
    private Long commId;

    @Schema(description = "请求单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String seqNo;


    @Schema(description = "到账账户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private FlowInAccountType flowInAccountType;


    @Schema(description = "客服审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date csOpTime;

    @Schema(description = "财务审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date financeOpTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date finishTime;

    @Schema(description = "客服备注")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String csNote;


    @Schema(description = "财务备注")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String financeNote;
}
