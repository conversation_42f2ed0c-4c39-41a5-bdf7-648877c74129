package com.cdz360.biz.model.cus.score.po;

import com.cdz360.biz.model.cus.score.type.DiscountType;
import com.cdz360.biz.model.cus.score.type.ScoreSettingStatusType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "积分体系")
public class ScoreSettingPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@ApiModelProperty(value = "体系名称")
	@NotNull(message = "name 不能为 null")
	@Size(max = 255, message = "name 长度不能超过 255")
	private String name;

	@ApiModelProperty(value = "生效开始时间，右闭区间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date startTime;

	@ApiModelProperty(value = "生效结束时间，左开区间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date endTime;

	@ApiModelProperty(value = "状态")
	private ScoreSettingStatusType status;

	@ApiModelProperty(value = "折扣类型，奖励规则")
	private DiscountType discountType;

	@ApiModelProperty(value = "操作人")
	private Long sysUid;

	@ApiModelProperty(value = "顶级商户ID")
	private Long topCommId;

	@ApiModelProperty(value = "是否可用 0-不可用，1-可用")
	@NotNull(message = "enable 不能为 null")
	private Boolean enable;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


}
