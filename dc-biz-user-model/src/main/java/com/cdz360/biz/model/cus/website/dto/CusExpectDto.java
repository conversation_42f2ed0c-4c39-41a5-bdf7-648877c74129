package com.cdz360.biz.model.cus.website.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "客户意向信息")
public class CusExpectDto {

    @Schema(description = "姓名")
    @NotNull(message = "name 不能为 null")
    @Size(max = 100, message = "name 长度不能超过 100")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @Schema(description = "联系电话")
    @NotNull(message = "phone 不能为 null")
    @Size(max = 20, message = "phone 长度不能超过 20")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String phone;



    @Schema(description = "省")
    @Size(max = 8, message = "province 长度不能超过 8")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String province;



    @Schema(description = "市")
    @Size(max = 8, message = "city 长度不能超过 8")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String city;



    @Schema(description = "当前情况或期望")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> context;



    @Schema(description = "想法")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String mind;
}
