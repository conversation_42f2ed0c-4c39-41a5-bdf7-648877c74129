package com.cdz360.biz.model.cus.website.po;


import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "客户意向信息")

public class CusExpectPo {



	@Schema(description = "记录ID")

	@NotNull(message = "id 不能为 null")

	private Long id;



	@Schema(description = "姓名")

	@NotNull(message = "name 不能为 null")

	@Size(max = 100, message = "name 长度不能超过 100")

	private String name;



	@Schema(description = "联系电话")

	@NotNull(message = "phone 不能为 null")

	@Size(max = 20, message = "phone 长度不能超过 20")

	private String phone;



	@Schema(description = "省")

	@Size(max = 8, message = "province 长度不能超过 8")

	private String province;



	@Schema(description = "市")

	@Size(max = 8, message = "city 长度不能超过 8")

	private String city;



	@Schema(description = "当前情况或期望")

	private List<String> context;



	@Schema(description = "想法")

	private String mind;



	@Schema(description = "创建时间")

	private Date createTime;



	@Schema(description = "更新时间")

	private Date updateTime;





}

