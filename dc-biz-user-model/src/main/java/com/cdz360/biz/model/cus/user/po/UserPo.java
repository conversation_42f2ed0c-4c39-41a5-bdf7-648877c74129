package com.cdz360.biz.model.cus.user.po;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UserPo {

    /**
     * 用户UID
     */
    private Long id;

    /**
     * 商户Id
     */
    private Long commId;

    /**
     * 互联合作方编号
     */
    private String partnerCode;

    /**
     *
     */
    private String username;

    /**
     * 密码
     */
    private String pwd;

    /**
     *
     */
    private String name;

    /**
     * 1.男,2.女
     */
    private Integer sex;

    /**
     * 应用来源(1.APP,2.微信 3.平台 4.支付宝,6.绑定卡片时注册)
     */
    private Integer sourceId;

    /**
     * 最后登录时间
     */
    private Date lastLogin;

    /**
     * 注册时间
     */
    private Date regTime;

    /**
     * 1.正常,0.禁用
     */
    private Integer stats;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 是否缴保证金(0.否,1.是，2为提现中)
     */
    private Integer isBond;

    /**
     * 头像
     */
    private String image;

    /**
     * 微信号
     */
    private String wechant;

    /**
     * 微博
     */
    private String microblog;

    /**
     *
     */
    private String qq;

    /**
     * 省
     */
    private Integer province;

    /**
     * 市
     */
    private String cityCode;

    /**
     * 区
     */
    private Integer district;

    /**
     * 街道
     */
    private String street;

    /**
     * 地址
     */
    private String address;

    /**
     * 邮编
     */
    private Integer postCode;

    /**
     * 国家号
     */
    private String nationalCode;

    /**
     * 手机号是否允许运营商访问(0.否,1.是)
     */
    private Integer allowOperator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否第一次登录(1.是,0.否)
     */
    private Integer isFirstLogin;

    /**
     *
     */
    private Double lng;

    /**
     *
     */
    private Double lat;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 是否tesla车主认证(0.否,1.是)
     */
    private Integer certified;

    /**
     * 生日
     */
    private String brithday;

    /**
     * 移车电话
     */
    private String movePhone;

    /**
     * 登录IP
     */
    private String loginIp;

    /**
     * 是否允许开启多笔订单 ，默认0-未开启;1-已开启
     */
    private Boolean isMulti;

    /**
     * 用户状态（10000-删除，10001-正常,10002-加入黑名单）
     */
    private Integer status;

    /**
     * 用户积分
     */
    private Long integral;

    /**
     * 用户等级id
     */
    private Integer userLevel;

    /**
     * 退款失败原因，临时记录
     */
    private String refundsFailReasons;

    /**
     * 退款失败原因是否已读，0 未读 1已读
     */
    private Integer refundsFailReasonsStatus;

    /**
     * 注册来源客户端详细类型
     */
    private String sourceType;

    /**
     * 支付宝userId
     */
    private String aliUserId = null;

    /**
     * 支付宝access_token
     */
    private String aliAccessToken;

    /**
     * 微信公众号openid
     */
    private String wxOpenId;

    /**
     * 微信unionID
     */
    private String wxUnionId = null;
    /**
     * 微信小程序openid
     */
    private String wechatOpenid;
    /**
     * 高德小程序openid
     */
    private String gaodeOpenid;
    /**
     * 默认扣款账户ID
     */
    private Long balanceId;
    /**
     * 默认扣款账户类型 1个人账户2集团授权账户
     */
    private Integer defaultPayType;

    @Schema(description = "国家地区代码",
        externalDocs = @ExternalDocumentation(
            description = "iso-3166 Country Codes",
            url = "https://www.iso.org/obp/ui/#search"))
    private String countryCode;

    @Schema(description = "BCP 47 语言标签(注册时可填写移动端设备默认语言)",
        externalDocs = @ExternalDocumentation(
            description = "BCP 47 Language Tags",
            url = "https://www.rfc-editor.org/info/bcp47<br />"
                + "https://www.countrycode.org/"))
    private String language;

    @Schema(description = "欠费状态: 未欠费(false), 欠费(true)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean debt;

    // 做字段兼容
    public Long getUid() {
        return id;
    }

    public Integer getCity() {
        try {
            return Integer.parseInt(this.cityCode);
        } catch (Exception e) {
            return null;
        }
    }
}
