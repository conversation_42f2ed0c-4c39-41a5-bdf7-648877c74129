package com.cdz360.biz.model.cus.discount.dto;

import com.cdz360.biz.model.discount.vo.CustomFee;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "协议价策略")
@EqualsAndHashCode(callSuper = true)
public class PriceStrategy extends BasePriceStrategy {
    @Schema(description = "协议价")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal discount;

    @Schema(description = "自定义尖峰平谷 协议价类型为14,15时存在")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CustomFee discountCustomFee;
}
