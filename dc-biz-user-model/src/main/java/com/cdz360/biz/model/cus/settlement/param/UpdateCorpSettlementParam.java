package com.cdz360.biz.model.cus.settlement.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.cus.settlement.SectionFee;
import com.cdz360.biz.model.cus.settlement.SettlementDiscount;
import com.cdz360.biz.model.cus.settlement.po.SettlementCfgPo;
import com.cdz360.biz.model.cus.settlement.type.SettlementCfgStatusEnum;
import com.cdz360.biz.model.cus.settlement.type.SubSettlementType;
import com.cdz360.biz.model.discount.vo.CustomFee;
import com.cdz360.biz.model.discount.vo.Fee;
import com.cdz360.biz.model.settle.vo.BillConfig;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UpdateCorpSettlementParam extends BaseObject {

    @Schema(description = "t_corp的id", required = true)
    private Long corpId;

    @Schema(description = "生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date activeDate;

    @Schema(description = "是否有保底消费, true/false")
    private Boolean guarantee;

    @Schema(description = "保底电量, <= 0 表示无保底电量要求: 单位(kW·h)")
    private Integer guaranteeKwh;

    @Schema(description = "保底电费单价: 单位(元/kW·h)")
    private BigDecimal guaranteeElecFee;

    @Schema(description = "保底服务费单价: 单位(元/kW·h)")
    private BigDecimal guaranteeServFee;

    @Schema(description = "价格方案: 0, 未知; 11, 固定价格; 12, 订单原价; 13, 自定义尖峰平谷; 14, 阶梯式单价; 15, 阶梯式分段",
        format = "java.lang.Integer")
    private SubSettlementType subSettlementType;

    @Schema(description = "账单自动生成日期 1 -- 28")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Integer settlementDate;

    @Schema(description = "首次账单生成年月", example = "2022-10")
    @JsonFormat(pattern = "yyyy-MM", locale = "zh", timezone = "GMT+8")
    private Date firstSettDate;

    @Schema(description = "账单自动生成个数")
    private Integer billNum;

    @Schema(description = "账单自动生成配置")
    private List<BillConfig> billConfigList;

    @Schema(description = "结算类型: UNKNOWN(0)-未知,BALANCE(1)-账户余额扣减(先付费),GUARANTEE(2)-担保消费结算,"
        +
        "POSTPAID(3)-预消费结算(后付费),PARTNER(4)-外部平台结算", required = true, format = "java.lang.Integer")
    private SettlementType settlementType;

    @Schema(description = "自动生成账单是否打开: true/false", required = true)
    private Boolean settlementSwitch;

    // 结算方案
    // { servFee: xxx, elecFee: xxx }
    @Schema(description = "结算方案为固定价格使用")
    private Fee fixed;

    // { jian: { servFee: xxx, elecFee: xxx }, feng: {xxx} , ping: {xxx}, gu: {xxx}
    @Schema(description = "结算方案为自定义尖峰平谷使用")
    private CustomFee customFee;

    // [{ startKwh: xxx, endKwh: xxx, servFee: xxx, elecFee: xxx }]
    @Schema(description = "结算方案为阶梯式(单价/分段)使用")
    private List<SectionFee> sectionFeeList;

    @Schema(description = "结算方案为订单折扣使用")
    private SettlementDiscount settDiscount;

    /**
     * 校验参数
     *
     * @param param
     */
    public static void checkValue(UpdateCorpSettlementParam param) {
        if (null == param) {
            throw new DcArgumentException("参数无效");
        }

        if (null == param.getCorpId()) {
            throw new DcArgumentException("请提供企业ID");
        }

//        if (param.getSettlementType() == null) {
//            throw new DcArgumentException("需要指定结算类型");
//        }

//        // 预付费结算不需要处理其他参数
//        if (param.getSettlementType() == SettlementType.BALANCE) {
////            if (param.getGuarantee() != null ||
////                    param.getGuaranteeKwh() != null ||
////                    param.getGuaranteeElecFee() != null ||
////                    param.getGuaranteeServFee() != null) {
////                throw new DcArgumentException("预付费无需保底相关数据");
////            }
////
////            if (param.getSubSettlementType() != null) {
////                throw new DcArgumentException("预付费无需结算方案相关数据");
////            }
////
////            if (param.getSettlementSwitch() != null) {
////                throw new DcArgumentException("预付费无需账单生成设置相关数据");
////            }
//            return;
//        }

        // 保底电量
        if (param.getGuarantee() != null && param.getGuarantee()) {
            // 只能配置整数
            if (param.getGuaranteeKwh() == null) {
                throw new DcArgumentException("保底电量无效");
            }

            // 精确到小数点后4位
            if (param.getGuaranteeElecFee() == null) {
                throw new DcArgumentException("保底电费单价无效");
            }

            // 精确到小数点后4位
            if (param.getGuaranteeServFee() == null) {
                throw new DcArgumentException("保底服务费单价无效");
            }
//        } else {
//            param.setGuaranteeKwh(null)
//                    .setGuaranteeElecFee(null)
//                    .setGuaranteeServFee(null);
        }

        // 自动账单生成
        if (param.getSettlementSwitch() != null && param.getSettlementSwitch()) {
            if (param.getSettlementDate() == null) {
                throw new DcArgumentException("账单生成日期无效");
            }

            if (param.getSettlementDate() < 1 || param.getSettlementDate() > 28) {
                throw new DcArgumentException("账单生成日期无效(范围: 1-28)");
            }

            if (param.getBillNum() == null || param.getBillNum() <= 0 || param.getBillNum() > 100) {
                throw new DcArgumentException("生成账单个数无效");
            }

            if (CollectionUtils.isEmpty(param.getBillConfigList())
                || !NumberUtils.equals(param.getBillNum(), param.getBillConfigList().size())) {
                throw new DcArgumentException("生成账单个数与账单生成配置不匹配");
            }

            List<String> temp = new ArrayList<>();
            AtomicInteger count = new AtomicInteger();
            param.getBillConfigList().forEach(e -> {

                if (StringUtils.isEmpty(e.getBillName()) || e.getBillName().length() > 20) {
                    throw new DcArgumentException("账单名称过长");
                }

                temp.addAll(e.getSiteIdList());
                count.set(count.get() + e.getSiteIdList().size());
            });
            if (count.get() != temp.stream().distinct().count()) {
                throw new DcArgumentException("站点重复");
            }

            // 结算方案
            if (param.getSubSettlementType() == null) {
                throw new DcArgumentException("结算方案无效");
            }
        } else {
            param.setSettlementDate(null);
            param.setFirstSettDate(null);
            param.setBillConfigList(null);
        }

        if (param.getSubSettlementType() != null) {
            switch (param.getSubSettlementType()) {
                case FIXED_PRICE:
                    if (param.getFixed() == null ||
                        param.getFixed().getElecFee() == null ||
                        param.getFixed().getServFee() == null) {
                        throw new DcArgumentException("固定价格方案数据不完整");
                    }
                    break;
                case COST_PRICE:
                    break;
                case CUSTOM_J_F_P_G:
                    if (param.getCustomFee() == null ||
                        param.getCustomFee().getJian() == null ||
                        param.getCustomFee().getFeng() == null ||
                        param.getCustomFee().getPing() == null ||
                        param.getCustomFee().getGu() == null) {
                        throw new DcArgumentException("自定义尖峰平谷方案数据不完整");
                    }
                    break;
                case STEP_UNIT_PRICE:
                case STEP_SECTION_PRICE:
                    if (CollectionUtils.isEmpty(param.getSectionFeeList())) {
                        String tip = param.getSubSettlementType().getDesc();
                        throw new DcArgumentException(tip + "方案数据不完整");
                    }
                    break;
            }
        }
    }

    public SettlementCfgPo toSettlementCfgPo() {
        SettlementCfgPo result = new SettlementCfgPo();
        BeanUtils.copyProperties(this, result);
        result.setStatus(SettlementCfgStatusEnum.INACTIVE); // 默认无效

//        if (this.getSettlementType() == SettlementType.BALANCE) {
//            return result;
//        }

        // 场站
//        if (null != this.getSettlementDate()) {
//            result.setSiteIdList(JsonUtils.toJsonString(this.getSiteIdList()));
//        }

        // 结算方案
        if (null != getSubSettlementType()) {
            switch (this.getSubSettlementType()) {
                case FIXED_PRICE:
                    result.setSubSettlement(JsonUtils.toJsonString(this.getFixed()));
                    break;
                case COST_PRICE:
                    break;
                case CUSTOM_J_F_P_G:
                    result.setSubSettlement(JsonUtils.toJsonString(this.getCustomFee()));
                    break;
                case STEP_UNIT_PRICE:
                case STEP_SECTION_PRICE:
                    result.setSubSettlement(JsonUtils.toJsonString(this.getSectionFeeList()));
                    break;
                case ORDER_DISCOUNT:
                    result.setSubSettlement(JsonUtils.toJsonString(this.getSettDiscount()));
                    break;
            }
        }

        return result;
    }
}
