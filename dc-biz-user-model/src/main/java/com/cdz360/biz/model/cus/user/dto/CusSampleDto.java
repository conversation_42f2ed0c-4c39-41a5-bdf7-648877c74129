package com.cdz360.biz.model.cus.user.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CusSampleDto {
    /**
     * 用户UID
     */
    @JsonInclude(Include.NON_NULL)
    private Long id;

    /**
     * 商户Id
     */
    @JsonInclude(Include.NON_NULL)
    private Long commId;

    @JsonInclude(Include.NON_NULL)
    private Long topCommId;

    /**
     *
     */
    @JsonInclude(Include.NON_EMPTY)
    private String username;

    /**
     *
     */
    @JsonInclude(Include.NON_EMPTY)
    private String name;

    /**
     * 手机号
     */
    @JsonInclude(Include.NON_EMPTY)
    private String phone;

    @JsonInclude(Include.NON_EMPTY)
    @Schema(description = "邮箱")
    private String email;


    /**
     * 应用来源(1.APP,2.微信 3.平台 4.支付宝,6.绑定卡片时注册)
     */
    @JsonInclude(Include.NON_NULL)
    private Integer sourceId;

    /**
     * 注册时间
     */
    @JsonInclude(Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date regTime;

    @JsonInclude(Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @JsonInclude(Include.NON_NULL)
    @Schema(description = "账户余额", example = "123.45")
    private BigDecimal balance;

    @JsonInclude(Include.NON_NULL)
    private Integer status;

    @JsonInclude(Include.NON_NULL)
    private Boolean debt;

    /**
     * 头像
     */
    @JsonInclude(Include.NON_EMPTY)
    private String image;

    @Schema(description = "车牌号")
    @JsonInclude(Include.NON_NULL)
    private List<String> carNoList;

    @Schema(description = "引流人")
    @JsonInclude(Include.NON_NULL)
    private String referrer;

}
