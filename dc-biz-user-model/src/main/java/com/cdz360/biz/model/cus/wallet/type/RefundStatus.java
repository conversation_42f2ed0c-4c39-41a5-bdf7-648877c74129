package com.cdz360.biz.model.cus.wallet.type;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

@Getter
public enum RefundStatus implements DcEnum {

    UNKNOWN(0, "未知"),

    WAITING(1, "待处理"),

    CS_APPROVED(2, "客服审核"),

    CS_REJECT(3, "客服拒回"),

    APPROVED(4, "已审核"), // 银行打款中

    REJECTED(5, "已拒绝"),

    FINISH(20, "已打款"),

    AUTO_FAIL(21, "自动退款失败");

    private final int code;
    private final String desc;

    RefundStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RefundStatus valueOf(int code) {
        for (RefundStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return RefundStatus.UNKNOWN;
    }

}
