package com.cdz360.biz.model.cus.siteAuthCard.vo;

import com.cdz360.biz.model.cus.siteAuthCard.po.SiteAuthCardPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * SiteAuthCardVo
 *
 * @since 11/17/2021 3:32 PM
 * <AUTHOR>
 */
@Schema(description = "卡片本地鉴权VO")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class SiteAuthCardVo extends SiteAuthCardPo {
    private String siteName;
}