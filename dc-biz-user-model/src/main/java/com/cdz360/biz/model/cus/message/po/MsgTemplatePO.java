package com.cdz360.biz.model.cus.message.po;

import com.cdz360.biz.model.cus.message.type.MsgTemplate;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * MsgTemplatePO
 *  TODO
 * @since 2019/11/15 14:56
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MsgTemplatePO {
  private Long id;
  private Long topCommId;
  private MsgTemplate key;
  private String value;
  private String note;
  private Boolean enable;
}
