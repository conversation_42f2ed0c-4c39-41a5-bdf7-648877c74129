package com.cdz360.biz.model.cus.user.po;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.utils.JsonUtils;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UserOpenidPo {

    private Long id;
    private String type;
    private Long uid;
    private String openid;
    private String appId;
    private Long topCommId;
    /**
     * 额外数据1
     */
    private String extraA;
    /**
     * 额外数据2
     */
    private String extraB;
    private Date createTime;
    private Date updateTime;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
