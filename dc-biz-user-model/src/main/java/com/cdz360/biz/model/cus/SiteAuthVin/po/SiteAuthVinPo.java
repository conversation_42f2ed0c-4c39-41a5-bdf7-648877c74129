package com.cdz360.biz.model.cus.SiteAuthVin.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "VIN本地认证")
public class SiteAuthVinPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	private Long commId;

	@Size(max = 32, message = "vin 长度不能超过 32")
	private String vin;

	@Size(max = 32, message = "siteId 长度不能超过 32")
	private String siteId;

	@Schema(description = "1有效 0无效")
	private Boolean enable;

	private Date createTime;

	private Date updateTime;


}
