package com.cdz360.biz.model.cus.site.param;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "更新超停收费用户列表")
@Data
@Accessors(chain = true)
public class UpdateSiteParkFeeUserParam {
    @Schema(description = "站点编号")
    @NotNull(message = "siteId 不能为 null")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    private String siteId;

    @Schema(description = "用户ID列表(t_user.id)")
    @NotNull(message = "uidList 不能为 null")
    private List<Long> uidList;
}
