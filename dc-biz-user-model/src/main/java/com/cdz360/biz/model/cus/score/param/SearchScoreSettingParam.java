package com.cdz360.biz.model.cus.score.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.cus.score.type.ScoreSettingStatusType;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * SearchScoreSettingParam
 * 
 * @since 1/5/2023 4:28 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SearchScoreSettingParam extends BaseListParam {
    private String nameLike;
    private String siteGroupNameLike;
    private List<String> siteGidList;
    private Long id;
    private Long userId;
    private ScoreSettingStatusType status;
    private Long scoreMin = 0L;// 用户积分体系列表应超过最少积分，才会显示在体系列表中，必须与userId搭配使用

    // 管理账号生效站点组，保证所有体系能被覆盖
    private List<String> accountSiteGidList;

    private List<Long> excludeSettingIdList;

    private Long topCommId;

    private String siteId;
}