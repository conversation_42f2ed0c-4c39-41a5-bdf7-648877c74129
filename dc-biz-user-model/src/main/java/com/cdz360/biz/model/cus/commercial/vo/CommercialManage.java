package com.cdz360.biz.model.cus.commercial.vo;

import com.cdz360.biz.model.sys.constant.SmsOperatorType;
import lombok.Data;
import lombok.experimental.Accessors;


import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CommercialManage {
    private Long id;
    private Long comId;

    private String appName;

    private BigDecimal minChargeAmount;

    private BigDecimal minPrepayAmount;

    private String smsModelVerifyCodeNo;
    private String smsModelNationNo;
    private String smsModelAppNo;

    private SmsOperatorType smsOperatorType;

    private String smsApiKey;

    private String smsApiPwd;

    private String smsModelPersonCharge;
    private String smsModelMerchantCharge;
    private String smsModelCreditCharge;
    private String wxMsgApiKey;
    private String wxMsgTemplateRecharge;
    private String wxMsgTemplateRefund;
    private String wxMsgTemplateChargingEnd;//停止充电消息模板
    private String wxMsgTemplateChargingEndAbnormal;//异常订单消息模板
    private String wxMsgTemplateChargingStartAfter;
    private String wxMsgTemplateChargingStartPre;
    private String payApiKey;
    private String wxAppid;
    private String wxAppSecret;

    private String alipayAppletAppId;

    private String alipayPubKey;

    private String alipayPrvKey;

    private String uPushIosAppId;

    private String uPushIosAppSecret;

    private String uPushAndroidAppId;

    private String uPushAndroidAppSecret;

    private String wxGrantType;
    private String smsModelTimerChargeNo;

    private String serviceTel;

    private String invoinceUrl;


    private String invoiceDesc;

    private Boolean invoinceEnabled;

    private String wxLiteMgmAppId;

    private String wxLiteMgmSecret;

    private String wxServiceApiKey;

    /**
     * 桩管家微信公众号appid
     */
    private String wechatAppid;
    /**
     * 桩管家微信公众号secret
     */
    private String wechatAppSecret;
}
