package com.cdz360.biz.model.cus.wallet.vo;

import com.cdz360.biz.model.cus.wallet.type.RefundStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "退款申请单")
public class RefundOrderVo implements Serializable {
    private static final long serialVersionUID = -889974309406814081L;

    @Schema(description = "客户ID")
    private long cusId;

    @Schema(description = "银行卡ID")
    private long bankCardId;

    @Schema(description = "银行卡号")
    private String bankCardNo;

    @Schema(description = "提现金额, 单位'元', 2位小数")
    private BigDecimal amount;

    @Schema(description = "状态")
    private RefundStatus status;
}
