package com.cdz360.biz.model.cus.score.dto;

import com.cdz360.biz.model.cus.score.po.ScoreSettingLevelPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ScoreLevelDto
 *
 * @since 1/5/2023 4:54 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ScoreLevelDto {
//    @Schema(description = "商户会员ID")
//    private Long commCusId;

    @Schema(description = "积点")
    private BigDecimal score;

    @Schema(description = "对应等级")
    private Integer level;

    @Schema(description = "等级规则")
    private List<ScoreSettingLevelPo> levelList;
}