package com.cdz360.biz.model.cus.message.type;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

/**
 * 消息状态
 * <AUTHOR>
 */

@Getter
public enum EnableType  {
        SEND(true, "已发送" ),
        WITHDRAW(false, "已撤回"),
        ;

        private boolean code;
        private String desc;


    EnableType(boolean i, String s) {
        this.code = i;
        this.desc = s;
    }
}
