package com.cdz360.biz.model.cus.message.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.cus.message.type.MsgType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 站内信
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListMessageParam extends BaseListParam {

    @Schema(description = "用户ID")
    private Long uid;

    @Schema(description = "消息标题 模糊查询")
    private String title;

    @Schema(description = "状态")
    private Boolean enable;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @Schema(description = "消息类型")
    private MsgType msgType;

    @Schema(description = "发送人")
    private String username;

    @Schema(description = "接收平台")
    private Long platform;

    @Schema(description = "读取状态，true-未读，false-已读")
    private Boolean unread;


}
