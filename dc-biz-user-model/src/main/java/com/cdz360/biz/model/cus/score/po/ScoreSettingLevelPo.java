package com.cdz360.biz.model.cus.score.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.lang.Double;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "规则内容")
public class ScoreSettingLevelPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@ApiModelProperty(value = "积分体系id")
	private Long scoreSettingId;

	@ApiModelProperty(value = "会员等级")
	private Integer level;

	@ApiModelProperty(value = "最小积点")
	private Long minScore;

	@ApiModelProperty(value = "最大积点")
	private Long maxScore;

	@ApiModelProperty(value = "服务费折扣(%)")
	private BigDecimal discount;

	@ApiModelProperty(value = "固定总价(阶梯单价)")
	private BigDecimal totalPrice;

	@ApiModelProperty(value = "是否可用 0-不可用，1-可用")
	@NotNull(message = "enable 不能为 null")
	private Boolean enable;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


}
