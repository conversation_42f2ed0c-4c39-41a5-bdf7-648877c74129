package com.cdz360.biz.model.cus.score.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.lang.Double;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "生效站点组")
public class ScoreSettingSiteGroupPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@ApiModelProperty(value = "积分体系id")
	private Long scoreSettingId;

	@ApiModelProperty(value = "场站组id")
	@Size(max = 10, message = "gid 长度不能超过 10")
	private String gid;

	@ApiModelProperty(value = "是否可用 0-不可用，1-可用")
	@NotNull(message = "enable 不能为 null")
	private Boolean enable;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


}
