package com.cdz360.biz.model.cus.wallet.param;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.cus.wallet.type.RefundStatus;
import com.cdz360.biz.model.finance.type.FlowInAccountType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * RefundOrderParam
 *  TODO
 * @since 2019/11/1 10:23
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListRefundOrderParam extends BaseListParam {

    @Schema(description = "客户ID")
    private long cusId;

    @Schema(description = "提现来源(应用)")
    private List<AppClientType> appTypeList;

    @Schema(description = "状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private RefundStatus status;

//    @Schema(description = "集团商户ID")
//    private long topCommId;

    @Schema(description = "请求单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String seqNo;

//    @Schema(description = "到账账户")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private FlowInAccountType flowInAccountType;

    @Schema(description = "到账账户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<FlowInAccountType> flowInAccountTypeList;

    @Schema(description = "查询提现时间-from")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTimeFrom;

    @Schema(description = "查询提现时间-to")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTimeTo;

    @Schema(description = "查询完成时间-from")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date finishTimeFrom;

    @Schema(description = "查询完成时间-to")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date finishTimeTo;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
