package com.cdz360.biz.model.cus.user.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * WhiteCardVO
 *  下发紧急充电卡请求参数
 * @since 2019/7/10 14:57
 * <AUTHOR>
 *    <EMAIL>
 */
@Data
public class WhiteCardDto implements Serializable {
    /**
     * 场站ID
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;
    private List<WhiteCard> whiteCardList;

    private List<WhiteCardEvse> whiteCardEvses;

    /**
     * 若有值，则认为单桩下发紧急卡
     */
    private String singleEvseNo;
}
