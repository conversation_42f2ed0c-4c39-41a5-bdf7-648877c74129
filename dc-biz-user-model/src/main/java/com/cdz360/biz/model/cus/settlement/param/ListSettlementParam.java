package com.cdz360.biz.model.cus.settlement.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.corp.type.CorpType;
import com.cdz360.biz.model.cus.settlement.type.SettlementStatusEnum;
import com.cdz360.biz.model.cus.settlement.type.SubSettlementType;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取企业结算方案列表请求参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListSettlementParam extends BaseListParam {

    @Schema(description = "结算账单编号 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String billNo;

    @Schema(description = "结算账单名称 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String billName;

    @Schema(description = "账单编号列表 不支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @NotEmpty
    private List<String> billNoList;

    @Schema(description = "需排除的账单编号列表 不支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> exclusiveBillNoList;

    @Schema(description = "企业名称")
    private String corpName;

    @Schema(description = "企业Id")
    private Long corpId;

    @Schema(description = "企业类型")
    private CorpType corpType;

    @Schema(description = "创建者:编辑不更新操作人的信息, 系统自动生成填充: 系统")
    private String opName;

    @Schema(description = "结算方案类型")
    private List<SubSettlementType> subSettlementTypeList;

    @Schema(description = "结算状态")
    private List<SettlementStatusEnum> statusList;

    @Schema(description = "创建时间过滤条件")
    private TimeFilter createTimeFilter;

    @Schema(description = "账期过滤条件")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter settDate;

    @Schema(description = "商户ID链", example = "34474,34475", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @Schema(description = "是否忽略商户ID链(true:忽略;false or null:不忽略)")
    @JsonInclude(Include.NON_NULL)
    private Boolean ignoreCommIdChain;

    @Schema(description = "是否在企业客户申请开票页面 不做前端入参", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean inCorpInvoice;

    @Schema(description = "企业客户申请开票记录的申请单号 仅作参数传递，与 settlement 无关")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String applyNo;

    @Schema(description = "开票状态筛选")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TaxStatus taxStatus;

}
