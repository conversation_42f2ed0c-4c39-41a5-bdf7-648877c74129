package com.cdz360.biz.model.cus.basic.vo;

import com.cdz360.biz.model.cus.user.po.UserPo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserVo extends UserPo {


    /**
     * 券自动抵扣
     */
    private Boolean couponAutoDeduct;
}
