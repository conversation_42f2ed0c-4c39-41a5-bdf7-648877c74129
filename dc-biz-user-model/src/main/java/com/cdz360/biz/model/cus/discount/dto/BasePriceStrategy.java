package com.cdz360.biz.model.cus.discount.dto;

import com.cdz360.biz.model.discount.type.ProtocolType;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "协议价基础类")
@Data
@Accessors(chain = true)
public class BasePriceStrategy {

    @Schema(description = "协议价类型: 11(固定服务费); 12(折扣比例);" +
            " 13(固定总价); 14(自定义尖峰平谷); 15(自定义服务费尖峰平谷); 0(未知)")
    @NotNull(message = "type 不能为 null")
    private ProtocolType type;
}
