package com.cdz360.biz.model.cus.settlement.vo;

import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.cus.discount.dto.SiteDiscount;
import com.cdz360.biz.model.cus.settlement.SectionFee;
import com.cdz360.biz.model.cus.settlement.SettlementDiscount;
import com.cdz360.biz.model.cus.settlement.po.SettlementCfgPo;
import com.cdz360.biz.model.discount.vo.CustomFee;
import com.cdz360.biz.model.discount.vo.Fee;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CorpSettlementCfgVo extends SettlementCfgPo {
    @Schema(description = "企业平台开票功能是否打开: true -- 打开; false -- 非打开", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean invoiceOpen;

    @Schema(description = "当前生效的结算类型: UNKNOWN(0)-未知,BALANCE(1)-账户余额扣减(先付费),GUARANTEE(2)-担保消费结算," +
            "POSTPAID(3)-预消费结算(后付费),PARTNER(4)-外部平台结算", format = "java.lang.Integer")
    private SettlementType curSettlementType;

    @Schema(description = "生效结算类型: UNKNOWN(0)-未知,BALANCE(1)-账户余额扣减(先付费),GUARANTEE(2)-担保消费结算," +
            "POSTPAID(3)-预消费结算(后付费),PARTNER(4)-外部平台结算 还未生效的", format = "java.lang.Integer")
    private SettlementType futureSettlementType;

    @Schema(description = "生效结算类型的生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date futureActiveDate;

    @Schema(description = "自动生成账单是否打开: true/false")
    private Boolean settlementSwitch;

    // 结算方案
    // { servFee: xxx, elecFee: xxx }
    @Schema(description = "结算方案为固定价格使用")
    private Fee fixed;

    // { jian: { servFee: xxx, elecFee: xxx }, feng: {xxx} , ping: {xxx}, gu: {xxx}
    @Schema(description = "结算方案为自定义尖峰平谷使用")
    private CustomFee customFee;

    // [{ startKwh: xxx, endKwh: xxx, servFee: xxx, elecFee: xxx }]
    @Schema(description = "结算方案为阶梯式(单价/分段)使用")
    private List<SectionFee> sectionFeeList;

    @Schema(description = "预付费场站协议价配置")
    private List<SiteDiscount> siteDiscountList;

    @Schema(description = "结算方案为订单折扣使用")
    private SettlementDiscount settDiscount;

    /**
     * 将 json 格式转换成对象格式
     */
    public void initFee() {
        if (null == this.getSubSettlementType()) {
            return;
        }

        if (StringUtils.isNotBlank(this.getSubSettlement())) {
            switch (this.getSubSettlementType()) {
                case COST_PRICE:
                    // nothing to do
                    break;
                case STEP_SECTION_PRICE:
                case STEP_UNIT_PRICE:
                    this.sectionFeeList = JsonUtils.fromJson(this.getSubSettlement(), new TypeReference<List<SectionFee>>() {
                    });
                    break;
                case CUSTOM_J_F_P_G:
                    this.customFee = JsonUtils.fromJson(this.getSubSettlement(), CustomFee.class);
                    break;
                case FIXED_PRICE:
                    this.fixed = JsonUtils.fromJson(this.getSubSettlement(), Fee.class);
                    break;
                case ORDER_DISCOUNT:
                    this.settDiscount = JsonUtils.fromJson(this.getSubSettlement(),
                        SettlementDiscount.class);
                    break;
            }
        }
    }
}
