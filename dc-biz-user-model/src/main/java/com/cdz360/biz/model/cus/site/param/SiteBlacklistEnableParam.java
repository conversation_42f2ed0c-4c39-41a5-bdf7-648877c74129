package com.cdz360.biz.model.cus.site.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.cus.site.type.BlacklistFromType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;

@Data
@Schema(description = "场站黑名单使能操作")
@Slf4j
@Accessors(chain = true)
public class SiteBlacklistEnableParam {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "站点ID")
    private String siteId;

    @Schema(description = "站点名称,用于记录日志")
    private String siteName;

    @Schema(description = "企业名称,用于记录日志")
    private String corpName;

    @Schema(description = "用户ID")
    private Long uid;

    @Schema(description = "用户手机号,用于记录日志")
    private String phone;

    @Schema(description = "true,在黑名单; false,不在黑名单", hidden = true)
    private Boolean enable;

    @Schema(description = "企业id")
    private Long corpId;

    @Schema(description = "顶级商户id", hidden = true)
    private Long topCommId;

    @Schema(description = "拉黑原因：手动新增、停充超时")
    private BlacklistFromType from;

    public static void checkValueInBlacklist(SiteBlacklistEnableParam param) {
        if (StringUtils.isBlank(param.siteId)) {
            throw new DcArgumentException("请提供场站Id", Level.WARN);
        }

//        if (null == param.uid) {
//            throw new DcArgumentException("请提供用户Id值", Level.WARN);
//        }
    }


    public static void checkValueEnable(SiteBlacklistEnableParam param) {
        checkValueInBlacklist(param);

        if (null == param.enable) {
            throw new DcArgumentException("需要指定用户黑名单状态", Level.WARN);
        }
    }
}
