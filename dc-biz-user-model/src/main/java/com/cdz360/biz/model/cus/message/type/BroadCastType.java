package com.cdz360.biz.model.cus.message.type;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

/**
 * 消息发送对象
 * <AUTHOR>
 */

@Getter
public enum BroadCastType  {
        ALL(true, "所有用户" ),
        PART(false, "部分用户"),
        ;

        private boolean code;
        private String desc;


    BroadCastType(boolean i, String s) {
        this.code = i;
        this.desc = s;
    }
}
