package com.cdz360.biz.model.cus.discount.po;


import com.cdz360.biz.model.cus.discount.dto.BasePriceStrategy;
import com.cdz360.biz.model.discount.type.ProtocolType;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;



@Data

@Accessors(chain = true)

@Schema(description = "协议价方案")

public class DiscountStrategyPo {



	@Schema(description = "主键")

	@NotNull(message = "id 不能为 null")

	private Long id;




	@Schema(description = "协议价类型: 11(固定服务费); 12(折扣比例);" +
			" 13(固定总价); 14(自定义尖峰平谷); 15(自定义服务费尖峰平谷); 0(未知)")

	@NotNull(message = "type 不能为 null")

	private ProtocolType type;



	@Schema(description = "协议价方案")

	private BasePriceStrategy strategy;





}

