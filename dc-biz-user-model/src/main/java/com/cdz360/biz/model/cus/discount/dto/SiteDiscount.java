package com.cdz360.biz.model.cus.discount.dto;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.discount.type.ProtocolType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "场站协议价策略")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SiteDiscount extends BaseObject {
    @Schema(description = "场站ID列表", required = true)
    @NotNull(message = "需要指定场站ID列表")
    private List<String> siteIdList;

    @Schema(description = "场站名称列表", hidden = true)
    private List<String> siteNameList;

    @Schema(description = "协议价类型: 11(固定服务费); 12(折扣比例);" +
            " 13(固定总价); 14(自定义尖峰平谷); 15(自定义服务费尖峰平谷); 0(未知)", required = true)
    @NotNull(message = "type 不能为 null")
    private ProtocolType type;

    @Schema(description = "协议价方案", required = true)
    @NotNull(message = "协议价方案不能为空")
    private PriceStrategy strategy;
}
