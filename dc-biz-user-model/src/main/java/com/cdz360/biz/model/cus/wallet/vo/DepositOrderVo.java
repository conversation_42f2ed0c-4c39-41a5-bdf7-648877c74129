package com.cdz360.biz.model.cus.wallet.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "充值单返回")
@Data
@Accessors(chain = true)
public class DepositOrderVo {

    @Schema(description = "二维码图片 微信H5支付")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String codeUrl;
}
