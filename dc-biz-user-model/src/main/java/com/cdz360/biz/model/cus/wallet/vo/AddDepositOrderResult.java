package com.cdz360.biz.model.cus.wallet.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "充值订单创建结果")
public class AddDepositOrderResult {

    @Schema(description = "微信/支付宝支付单号")
    private String thirdPayOrderNo;

    @Schema(description = "我方支付单号")
    private String payTradeNo;

    @Schema(description = "时间戳")
    private String timeStamp;

    @Schema(description = "随机字符串")
    private String nonce;


    @Schema(description = "支付请求报文 微信支付的package部分; 支付宝支付的orderStr部分")
    private String payReqMsg;


    @Schema(description = "签名方式. MD5")
    private String paySignType;

    @Schema(description = "签名")
    private String paySign;

    @Schema(title = "微信子商户appId")
    private String appId;
    
    @Schema(description = "商户号")
    private String mchId;//商户号
    @Schema(description = "预会话Id")
    private String prepayId;//预会话Id
}
