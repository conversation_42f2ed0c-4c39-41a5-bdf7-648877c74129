package com.cdz360.biz.model.cus.settlement.po;

import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.biz.model.cus.settlement.type.GuaranteeWay;
import com.cdz360.biz.model.cus.settlement.type.SettlementStatusEnum;
import com.cdz360.biz.model.cus.settlement.type.SubSettlementType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "结算单")
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SettlementPo extends BaseObject {

    @Schema(description = "账单号")
    private String billNo;

    @Schema(description = "账单名称")
    private String billName;

    @Schema(description = "账期开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date settStartDateDay;

    @Schema(description = "账期结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date settEndDateDay;

    @Schema(description = "账期开始年月")
    @JsonFormat(pattern = "yyyy-MM", locale = "zh", timezone = "GMT+8")
    private Date settStartDate;

    @Schema(description = "账期结束年月")
    @JsonFormat(pattern = "yyyy-MM", locale = "zh", timezone = "GMT+8")
    private Date settEndDate;

    @Schema(description = "t_corp的id")
    private Long corpId;

    @Schema(description = "状态. INIT未结算; PAID已结算; CANCEL作废")
    private SettlementStatusEnum billStatus;

    @Schema(description = "结算类型: UNKNOWN(0)-未知,BALANCE(1)-账户余额扣减(先付费),GUARANTEE(2)-担保消费结算," +
        "POSTPAID(3)-预消费结算(后付费),PARTNER(4)-外部平台结算", format = "java.lang.Integer")
    private SettlementType settlementType;

    @Schema(description = "结算配置ID. t_settlement_cfg.id")
    private Long cfgId;

    @Schema(description = "结算保底电量方案: NOT_ENABLED -- 未开启; INACTIVE -- 未激活; ACTIVE -- 已激活")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private GuaranteeWay guaranteeWay;

    @Schema(description = "保底电量, <= 0 表示无保底电量要求: 单位(kW·h) 使用保底计算账单必填")
    private BigDecimal guaranteeKwh;

    @Schema(description = "保底电费单价: 单位(元/kW·h) 使用保底计算账单必填")
    private BigDecimal guaranteeElecFee;

    @Schema(description = "保底服务费单价: 单位(元/kW·h) 使用保底计算账单必填")
    private BigDecimal guaranteeServFee;

    @Schema(description = "价格方案: 0, 未知; 11, 固定价格; 12, 订单原价; 13, 自定义尖峰平谷; 14, 阶梯式单价; 15, 阶梯式分段",
        format = "java.lang.Integer", required = true)
    private SubSettlementType subSettlementType;

    @Schema(description = "结算方案")
    private String subSettlement;

    @Schema(description = "已开票金额: 单位: 元")
    private BigDecimal invoicedAmount;

    @Schema(description = "账单总额, 单位: 元")
    private BigDecimal settlementTotalFee;

    @Schema(description = "账单服务费, 单位: 元")
    private BigDecimal settlementServFee;

    @Schema(description = "账单电费, 单位: 元")
    private BigDecimal settlementElecFee;

    @Schema(description = "其他费用, 单位: 元")
    private BigDecimal settlementOtherFee;

    @Schema(description = "充电订单总收益, 单位: 元")
    private BigDecimal orderElecProfit;

    @Schema(description = "订单服务费收益, 单位: 元")
    private BigDecimal orderServProfit;

    @Schema(description = "充电订单总收益, 单位: 元")
    private BigDecimal orderTotalProfit;

    @Schema(description = "关联的订单数")
    private Integer orderNum;

    @Schema(description = "充电订单总电量")
    private BigDecimal orderKwh;

    @Schema(description = "其他时段电量")
    private BigDecimal kwhOther;

    @Schema(description = "尖时段电量")
    private BigDecimal kwhJian;

    @Schema(description = "峰时段电量")
    private BigDecimal kwhFeng;

    @Schema(description = "平时段电量")
    private BigDecimal kwhPing;

    @Schema(description = "谷时段电量")
    private BigDecimal kwhGu;

    @Schema(description = "(原始)电费, 单位: 元")
    private BigDecimal orderElecFee;

    @Schema(description = "(原始)服务费, 单位: 元")
    private BigDecimal orderServFee;

    @Schema(description = "关联的场站名称")
    private List<String> siteNameList;

    @Schema(description = "关联的场站编号")
    private List<String> siteNoList;

    @Schema(description = "流程实例ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String procInstId;

    @Schema(description = "操作人类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserType opType;  // UserType

    @Schema(description = "操作人ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long opId;

    @Schema(description = "操作人姓名")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String opName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;
}
