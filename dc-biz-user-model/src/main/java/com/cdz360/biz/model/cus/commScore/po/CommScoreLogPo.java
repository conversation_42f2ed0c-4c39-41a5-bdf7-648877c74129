package com.cdz360.biz.model.cus.commScore.po;

import com.cdz360.biz.model.cus.commScore.type.ScoreChangeReason;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class CommScoreLogPo {

    private Long id;

    private Long sysUid;

    private String sysUSerName;

    private Long commId;

    private Long userId;

    private BigDecimal changeBefore;

    private BigDecimal changeAfter;

    private ScoreChangeReason changeReason;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;
}
