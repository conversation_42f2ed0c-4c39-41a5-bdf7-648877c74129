package com.cdz360.biz.model.cus.settlement.vo;

import com.cdz360.base.model.base.vo.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SettlementBi extends BaseObject {
    @Schema(description = "账单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String billNo;

    @Schema(description = "账单名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String billName;

    @Schema(description = "账单服务费, 单位: 元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal settlementServFee;

    @Schema(description = "账单电费, 单位: 元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal settlementElecFee;

    @Schema(description = "账单总额, 单位: 元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal settlementTotalFee;
}
