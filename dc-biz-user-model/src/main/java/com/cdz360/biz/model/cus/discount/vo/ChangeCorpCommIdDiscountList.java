package com.cdz360.biz.model.cus.discount.vo;

import com.cdz360.biz.model.cus.site.vo.SiteSimpleVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "变更企业客户所属商户协议价影响列表")
public class ChangeCorpCommIdDiscountList {
    @Schema(description = "需要移除的场站列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SiteSimpleVo> removeList;

    @Schema(description = "保留场站列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SiteSimpleVo> remainList;
}
