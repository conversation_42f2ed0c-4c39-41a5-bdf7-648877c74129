package com.cdz360.biz.model.cus.message.po;

import com.cdz360.biz.model.common.po.BasePo;
import com.cdz360.biz.model.cus.message.type.BroadCastType;
import com.cdz360.biz.model.cus.message.type.MsgType;
import com.cdz360.biz.model.cus.message.type.PlatformType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MessagePo extends BasePo {

    /**
     * 消息ID
     */
    private Long id;

    @Schema(description = "目标用户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> targetUid;

    /**
     * 操作人ID
     */
    private Long opUid;

    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;
    /**
     * 接收平台 22-企业平台，20-管理平台&桩管家
     */
    private List<PlatformType> platformList;

    /**
     * 企业ID
     */
    private Long corpId;

    /**
     * 接收平台
     */
    private PlatformType platform;
    /**
     * 状态  1-已发送，2-已撤回
     */
    private Long status;

    /**
     * 消息类型  0-未知，1-升级通知，2-平台公告，3-续费提醒，4-其他消息
     */
    private MsgType msgType;

    /**
     * 接收对象 0-未知，1-所有用户，2-部分用户
     */
    private BroadCastType broadcast;

    /**
     * 接收对象部分商户时，商户ID，逗号分隔
     */
    private List<Long> commIdList;

    /**
     *
     */
    private String commIds;

}
