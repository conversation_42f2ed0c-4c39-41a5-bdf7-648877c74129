package com.cdz360.biz.model.cus.score.param;

import com.cdz360.biz.model.cus.score.po.ScoreSettingLevelPo;
import com.cdz360.biz.model.cus.score.po.ScoreSettingPo;
import com.cdz360.biz.model.cus.score.po.ScoreSettingRulePo;
import com.cdz360.biz.model.cus.score.po.ScoreSettingSiteGroupPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * ScoreSettingParam
 *
 * @since 1/5/2023 3:57 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
//@EqualsAndHashCode(callSuper = true)
public class ScoreSettingParam {
    private ScoreSettingPo scoreSettingPo;
    private List<ScoreSettingLevelPo> scoreSettingLevelPos;
    private List<ScoreSettingSiteGroupPo> scoreSettingSiteGroupPos;
    private ScoreSettingRulePo scoreSettingRulePo;

    @Schema(description = "顶级商户ID",hidden = true)
    private Long topCommId;
    @Schema(description = "1-包含用户，0-排除用户")
    private Boolean enable;

    @Schema(description = "0-全部，1-部分")
    private Boolean isAll;
    @Schema(description = "用户列表")
    private List<String> cusPhoneList;
}