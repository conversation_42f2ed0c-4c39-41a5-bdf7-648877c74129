package com.cdz360.biz.model.cus.SiteAuthVin.vo;

import com.cdz360.biz.model.cus.SiteAuthVin.po.SiteAuthVinPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * SiteAuthVinVo
 *
 * @since 11/17/2021 3:32 PM
 * <AUTHOR>
 */
@Schema(description = "VIN本地认证VO")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class SiteAuthVinVo extends SiteAuthVinPo {
    private String siteName;
}