package com.cdz360.biz.model.cus.settlement.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum SubSettlementType implements DcEnum {
    UNKNOWN(0, "未知"),
    FIXED_PRICE(11, "固定价格"),
    COST_PRICE(12, "订单原价"),
    CUSTOM_J_F_P_G(13, "自定义尖峰平谷"),
    STEP_UNIT_PRICE(14, "阶梯式单价"),
    STEP_SECTION_PRICE(15, "阶梯式分段"),
    ORDER_DISCOUNT(16, "订单折扣"),
    ;

    @JsonValue
    private final int code;

    private final String desc;

    SubSettlementType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static SubSettlementType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (SubSettlementType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return SubSettlementType.UNKNOWN;
    }
}
