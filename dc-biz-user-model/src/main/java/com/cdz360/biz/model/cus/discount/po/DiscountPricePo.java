package com.cdz360.biz.model.cus.discount.po;


import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.biz.model.cus.discount.type.DiscountStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;



@Data

@Accessors(chain = true)

@Schema(description = "预付费协议价配置")

public class DiscountPricePo {



	@Schema(description = "绑定账号ID(t_user.id)")

	@NotNull(message = "uid 不能为 null")

	private Long uid;



	@Schema(description = "帐户类型: 0(未知); 1(个人帐户); 2(授信帐户); 3(商户会员); 4(即充即退); 5(企业客户)")

	@NotNull(message = "accountType 不能为 null")

	private PayAccountType accountType;



	@Schema(description = "账户编号: 商户会员时为所属商户的商户ID，其他账户时为集团商户ID")

	@NotNull(message = "accountCode 不能为 null")

	private Long accountCode;



	@Schema(description = "站点编号")

	@NotNull(message = "siteId 不能为 null")

	@Size(max = 32, message = "siteId 长度不能超过 32")

	private String siteId;



	@Schema(description = "配置状态: 0(禁用)/1(启用)/2(待启用)")

	private DiscountStatus status;



	@Schema(description = "协议价方案ID(t_discount_strategy.id)")

	@NotNull(message = "discountRefId 不能为 null")

	private Long discountRefId;



	@Schema(description = "创建时间")

	private Date createTime;



	@Schema(description = "更新时间")

	private Date updateTime;



	@Schema(description = "操作人ID")

	private Long updateOpId;



	@Schema(description = "操作人名字")

	@Size(max = 32, message = "updateOpName 长度不能超过 32")

	private String updateOpName;





}

