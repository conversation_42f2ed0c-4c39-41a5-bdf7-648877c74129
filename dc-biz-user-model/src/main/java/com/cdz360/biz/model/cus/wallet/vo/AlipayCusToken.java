package com.cdz360.biz.model.cus.wallet.vo;

import com.cdz360.base.model.app.type.AppClientType;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "支付宝用户Token信息")
public class AlipayCusToken {

    @JsonProperty("user_id")
    @Schema(description = "支付宝用户的唯一标识。以2088开头的16位数字")
    private String userId;

    @JsonProperty("access_token")
    @Schema(description = "访问令牌。通过该令牌调用需要授权类接口")
    private String accessToken;

    @JsonProperty("expires_in")
    @Schema(description = "访问令牌的有效时间，单位是秒。")
    private Long expiresIn;

    @JsonProperty("refresh_token")
    @Schema(description = "刷新令牌。通过该令牌可以刷新access_token")
    private String refreshToken;

    @JsonProperty("re_expires_in")
    @Schema(description = "刷新令牌的有效时间，单位是秒。")
    private Long reExpiresIn;

    @JsonProperty("auth_start")
    @Schema(description = "授权token开始时间，作为有效期计算的起点")
    private String authStart;

    private Long topCommId;

    private AppClientType clientType;
}
