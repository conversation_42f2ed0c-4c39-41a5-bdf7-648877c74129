package com.cdz360.biz.model.cus.user.dto;

import com.cdz360.biz.model.cus.basic.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class UserAndBalanceAndTokenVo extends UserVo implements Serializable {

    /**
     * 用户所属商户id
     */
    private Long appCommId;

    /**
     * 金额(单位为分)
     */
    private long balance;


    /**
     * 当前活跃卡号
     */
    private Long cardId;


//    /**
//     * 是否缴保证金(0.否,1.是，2为提现中)
//     */
//    private Integer isBond;
    /**
     * 保证金
     */
    private long bond;

    /**
     * 赠送金
     */
    private long freeGold;

    private String token;

    private static final long serialVersionUID = 1L;

}
