package com.cdz360.biz.model.cus.user.param;

import com.cdz360.base.model.base.type.SexType;
import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

@Data
@Schema(description = "修改客户信息参数")
public class ModifyCusInfoParam {


    @Schema(description = "客户ID", hidden = true)
    private Long cusId;

    @Schema(description = "客户昵称, null或不传表示不更新")
    private String nickname;

    @Schema(description = "性别. MALE, 男; FEMALE, 女; UNKNOWN, 未知. null或不传表示不更新")
    private SexType sex;

    @Schema(description = "城市编码", example = "311001")
    private String cityCode;

    @Schema(description = "出生日期, YYYY-MM-DD. null或不传表示不更新", example = "2019-10-21")
    private Date birthday;

    /**
     * 字典表 type = eduDegree
     */
    @Schema(description = "学历. 0,其他; 1,小学; 2,初中; 3,中专; 4,高中; 5专科; 6,本科; 7,硕士; 8,博士; 9,博士后; 10,保密")
    private Integer degree;

    @Schema(description = "收入. 1, 3000 元以下/月; 5, 3000 元以上/月; 10, 5000 元以上/月; 15, 10000 元以上/月; 20, 20000 元以上/月; 25, 50 万元以上/年; 30, 100 万元以上/年; 99, 保密")
    private Integer salaryLevel;

    @Schema(description = "客户头像URL. 文件上传的URL格式要求为 /cus/headIcon/${uid}_{timestamp}.${suffix} null或不传表示不更新",
            example = "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/cus/headIcon/1234_20191104152341.jpg"
    )
    private String headIconUrl;

    private String phone;

    @Schema(description = "支付宝、微信appid")
    private String appId;

    @Schema(description = "修改微信openid 用于微信帐号绑定, 传none表示解绑", example = "abcd1234")
    private String wxOpenId;

    @Schema(description = "修改微信unionid 用于微信帐号绑定, 传none表示解绑", example = "abcd1234")
    private String wxUnionId;

    @Schema(description = "修改支付宝user_id 用于支付宝帐号绑定, 传none表示解绑", example = "abcd1234")
    private String alipayUserId;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
