package com.cdz360.biz.model.cus.score.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "积分等级信息")
@Data
@Accessors(chain = true)
public class ScoreSettingLevelDto {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @ApiModelProperty(value = "积分体系id")
    private Long scoreSettingId;

    @ApiModelProperty(value = "会员等级")
    private Integer level;

    @ApiModelProperty(value = "最小积点")
    private Long minScore;

    @ApiModelProperty(value = "最大积点")
    private Long maxScore;

    @ApiModelProperty(value = "服务费折扣(%)")
    private BigDecimal discount;

    @ApiModelProperty(value = "固定总价(阶梯单价)")
    private BigDecimal totalPrice;

}
