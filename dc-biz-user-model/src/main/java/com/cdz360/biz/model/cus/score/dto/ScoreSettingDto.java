package com.cdz360.biz.model.cus.score.dto;

import com.cdz360.biz.model.cus.score.po.ScoreSettingLevelPo;
import com.cdz360.biz.model.cus.score.po.ScoreSettingPo;
import com.cdz360.biz.model.cus.score.po.ScoreSettingSiteGroupPo;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * ScoreSettingDto
 *
 * @since 1/6/2023 9:51 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ScoreSettingDto extends ScoreSettingPo {
    private List<SiteGroupVo> siteGroupVos;
    private List<String> siteIds;//从场站组转换而来的场站id
    private String sysUserName;
    private List<ScoreSettingLevelPo> scoreSettingLevelPos;
    private BigDecimal userScore;//当前体系下，用户对应的积分
    private ScoreSettingLevelPo currentLevel;//当前积分对应的等级
}