package com.cdz360.biz.model.cus.settlement.vo;

import com.cdz360.biz.model.cus.settlement.po.SettlementPo;
import com.cdz360.biz.model.cus.settlement.type.GuaranteeWay;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "企业结算方式配置页面查看")
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SettlementVo extends SettlementPo implements Serializable {

    @Schema(description = "企业结算配置信息", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CorpSettlementCfgVo settlementCfg;

    @Schema(description = "企业名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String corpName;

    @Schema(description = "开票状态", example = "NO")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TaxStatus taxStatus;

    @Schema(description = "企业开票抬头")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String name;

    @Schema(description = "销方名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String saleName;

    @Schema(description = "销方银行")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String saleBank;

    @Schema(description = "销方银行账户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String saleAccount;

    @Schema(description = "公司地址")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String address;

    @Schema(description = "账单电量，单位(kW·h)")
    public BigDecimal getBillKwh() {
        return GuaranteeWay.ACTIVE.equals(getGuaranteeWay()) ?
            getGuaranteeKwh() : getOrderKwh();
    }
}
