package com.cdz360.biz.model.cus.user.param;

import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.biz.model.common.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListCustomerParam extends BaseListParam {

    @Schema(description = "客户姓名")
    private String cusName;

    @Schema(description = "客户手机号")
    private String cusPhone;

    @Schema(description = "客户ID")
    private Long cusId;

    @Schema(description = "客户邮箱")
    private String email;

    @Schema(description = "注册时间过滤")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter regTimeFilter;

    /**
     * 应用来源(1.APP,2.微信 3.平台 4.支付宝,6.绑定卡片时注册)
     */
    @Schema(description = "客户来源")
    private Long sourceId;

    @Schema(description = "引流人")
    private String referrer;

    @Schema(description = "是否获取账户余额")
    private Boolean balance;

    @Schema(description = "是否是互联互通: true -- 查互联互通用户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean hlht;

    @Schema(description = "是否欠费,true-欠费，false-未欠费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean isDebt;

    @Schema(description = "用于转换成idChain")
    private Long leaderCommId;// 用于转换成idChain

    @Schema(description = "客户手机号")
    private List<String> cusPhoneList;

    @Schema(description = "商户ID列表", hidden = true)
    private List<Long> commIdList;

}
