package com.cdz360.biz.model.cus.wallet.vo;

import com.cdz360.base.model.bi.vo.OrderBi;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "用户中心信息")
public class CusProfileVo implements Serializable {
    private static final long serialVersionUID = 1304924442395006576L;

    @Schema(description = "客户ID")
    private long cusId;

    @Schema(description = "客户手机号")
    private String phone;

    @Schema(description = "客户昵称(微信/支付宝昵称)")
    private String nickname;

    @Schema(description = "客户姓名")
    private String name;

    @Schema(description = "客户头像URL")
    private String image;

    @Schema(description = "订单统计信息")
    private OrderBi orderBi;

    @Schema(description = "钱包摘要信息")
    private Wallet wallet;



    @Data
    @Accessors(chain = true)
    public static class Wallet implements Serializable {

        private static final long serialVersionUID = -1711707990182603938L;

        @Schema(description = "现金账户余额, 单位'元', 2位小数")
        private BigDecimal amount;

        @Schema(description = "授信账户数量")
        private Integer creditNum;

        @Schema(description = "商户会员数量")
        private Integer commNum;

        @Schema(description = "有效优惠券数量")
        private Long enableCouponNum;
    }
}
