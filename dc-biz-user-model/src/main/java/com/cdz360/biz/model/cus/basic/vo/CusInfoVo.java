package com.cdz360.biz.model.cus.basic.vo;

import com.cdz360.base.model.base.type.SexType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "客户信息")
@Data
@Accessors(chain = true)
public class CusInfoVo {


    @Schema(description = "客户ID", example = "123")
    private Long cusId;

    @Schema(description = "客户昵称, null或不传表示不更新")
    private String nickname;

    @Schema(description = "性别. MALE, 男; FEMALE, 女; UNKNOWN, 未知")
    private SexType sex;

    @Schema(description = "城市编码", example = "311001")
    private String cityCode;

    @Schema(description = "出生日期, YYYY-MM-DD", example = "2019-10-21")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    /**
     * 字典表 type = eduDegree
     */
    @Schema(description = "学历. 0,其他; 1,小学; 2,初中; 3,中专; 4,高中; 5专科; 6,本科; 7,硕士; 8,博士; 9,博士后; 10,保密")
    private Integer degree;

    @Schema(description = "收入. 1, 3000 元以下/月; 5, 3000 元以上/月; 10, 5000 元以上/月; 15, 10000 元以上/月; 20, 20000 元以上/月; 25, 50 万元以上/年; 30, 100 万元以上/年; 99, 保密")
    private Integer salaryLevel;


    @Schema(description = "客户头像URL.",
            example = "https://fourth-platform.oss-cn-shanghai.aliyuncs.com/cus/headIcon/1234_20191104152341.jpg")
    private String headIconUrl;

    @Schema(description = "微信unionid", example = "abcd1234")
    private String wxUnionId;

    @Schema(description = "支付宝user_id", example = "abcd1234")
    private String alipayUserId;
}
