package com.cdz360.biz.model.cus.wallet.type;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

/**
 * 小程序端钱包流水信息
 */
@Getter
public enum AppWalletLogType implements DcEnum {

    UNKNOWN(0, "未知"),

    PAYING(1, "待支付/充值失败"),

    RECHARGE(2, "充值"),

    PAY_ORDER(3, "支付订单"),

    REFUND(4, "提现"),

    FROZEN(5, "冻结"),

    UNFROZE(6, "解冻"),

    CASH(7, "扣费");

    private final int code;
    private final String desc;

    AppWalletLogType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AppWalletLogType codeOf(int code) {
        for (AppWalletLogType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return AppWalletLogType.UNKNOWN;
    }

}
