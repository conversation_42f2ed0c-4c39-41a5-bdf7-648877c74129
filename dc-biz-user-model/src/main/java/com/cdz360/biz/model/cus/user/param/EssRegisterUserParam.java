package com.cdz360.biz.model.cus.user.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "注册用户列表查询参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class EssRegisterUserParam extends BaseListParam {

    @Schema(description = "邮箱(支持模糊查询)")
    @JsonInclude(Include.NON_EMPTY)
    private String emailLike;

    @Schema(description = "用户状态: 10001(已启用); 10003(已禁用)")
    @JsonInclude(Include.NON_NULL)
    private Integer status;

    @Schema(description = "国家地区代码",
        externalDocs = @ExternalDocumentation(
            description = "iso-3166 Country Codes",
            url = "https://www.iso.org/obp/ui/#search"))
    @JsonInclude(Include.NON_NULL)
    private List<String> countryCodeList;
}
