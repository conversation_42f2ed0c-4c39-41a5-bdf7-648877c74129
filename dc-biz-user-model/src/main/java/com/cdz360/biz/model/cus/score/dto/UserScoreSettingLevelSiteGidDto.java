package com.cdz360.biz.model.cus.score.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * UserScoreSettingLevelSiteGidDto
 * 
 * @since 7/27/2023 1:56 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
//@EqualsAndHashCode(callSuper = true)
public class UserScoreSettingLevelSiteGidDto extends ScoreSettingSiteGidDto {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserScoreSettingLevelDto currentLevel;

    @Override
    public boolean equals(final Object obj) {
        if (obj == null) {
            return false;
        }
        if (obj instanceof UserScoreSettingLevelSiteGidDto) {
            return ((UserScoreSettingLevelSiteGidDto) obj).getId().equals(getId());
        } else {
            return false;
        }
    }

    @Override
    public int hashCode() {
        return getId().hashCode();
    }
}