package com.cdz360.biz.model.cus.wallet.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "退款原因分析")
public class RefundAnalyzeVo {

    @Schema(description = "电桩故障")
    private Integer evseErrorCount = 0;

    @Schema(description = "油车占位")
    private Integer occupiedByCarCount = 0;

    @Schema(description = "收费太贵")
    private Integer tooExpensiveCount = 0;

    @Schema(description = "客服服务")
    private Integer badCustomerServiceCount = 0;

    @Schema(description = "不愿充值")
    private Integer distrustCount = 0;

    @Schema(description = "其他问题")
    private Integer othersCount = 0;
}
