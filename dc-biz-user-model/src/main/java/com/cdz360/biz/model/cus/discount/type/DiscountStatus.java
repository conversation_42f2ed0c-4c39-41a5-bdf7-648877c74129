package com.cdz360.biz.model.cus.discount.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum DiscountStatus implements DcEnum {
    DISABLE(0, "禁用"),
    ENABLE(1, "启用"),
    WAIT(2, "待启用"),
    ;

    @JsonValue
    private final int code;

    private final String desc;

    DiscountStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static DiscountStatus valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (DiscountStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return DiscountStatus.DISABLE;
    }
}
