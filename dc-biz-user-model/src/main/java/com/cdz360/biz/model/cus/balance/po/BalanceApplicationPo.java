package com.cdz360.biz.model.cus.balance.po;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.model.cus.balance.type.BalanceApplicationStatusType;
import com.cdz360.biz.model.cus.balance.type.DepositFlowType;
import com.cdz360.biz.model.finance.type.FlowInAccountType;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.finance.type.TaxType;
import com.cdz360.biz.model.oa.dto.OaContract;
import com.cdz360.biz.model.oa.vo.PaymentPlan;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "账户金额变更申请")
public class BalanceApplicationPo implements Serializable {

    private static final long serialVersionUID = -1831363757456704926L;
    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "状态：NEW待初审, RECHECK待复审, CHECK_FAILED审核失败, FINISH已完成, REJECT复核失败, PASSED已复核")
    private BalanceApplicationStatusType status;

    @Schema(description = "CREDIT企业客户、COMMERCIAL商户会员、PERSONAL个人")
    private PayAccountType accountType;

    @Schema(description = "充值IN_FLOW、减少OUT_FLOW")
    private DepositFlowType flowType;

    @Schema(description = "账户所属商户,个人: 顶级商户Id; 商户会员: 会员所属商户Id; 企业客户: 企业客户所属顶级商户Id")
    private Long commId;

    @Schema(description = "充值实际金额, 单位''元'', 2位小数")
    private BigDecimal amount;

    @Schema(description = "充值赠送金额, 单位''元'', 2位小数")
    private BigDecimal freeAmount;

    @Schema(description = "在线退款金额, 单位''元'', 2位小数")
    private BigDecimal refundAmount;

    @Schema(description = "开票状态")
    private TaxStatus taxStatus;

    @Schema(description = "发票类型(税种): UNKNOWN(0)-未知, NORMAL_TAX(2)-个人普通发票, PREPAY_TAX(3)-企业普通发票, SPECIAL_VAT(5)-企业专业发票")
    private TaxType taxType;

    @Schema(description = "税票号")
    @Size(max = 20, message = "taxNo 长度不能超过 20")
    private String taxNo;

    @Schema(description = "备注")
    @Size(max = 255, message = "remark 长度不能超过 255")
    private String remark;

    @Schema(description = "支付方式")
    private PayChannel payChannel;

    @Schema(description = "支付账户银行名称")
    @Size(max = 255, message = "outBankName 长度不能超过 255")
    private String outBankName;

    @Schema(description = "支付账户银行卡号")
    @Size(max = 255, message = "outAccountNo 长度不能超过 255")
    private String outAccountNo;

    @Schema(description = "支付账户名称")
    @Size(max = 255, message = "outAccountName 长度不能超过 255")
    private String outAccountName;

    @Schema(description = "收款账户类型")
    private FlowInAccountType flowInAccountType;

    @Schema(description = "收款账户名称")
    @Size(max = 255, message = "inAccountName 长度不能超过 255")
    private String inAccountName;

    @Schema(description = "收款账户银行名称")
    @Size(max = 255, message = "inBankName 长度不能超过 255")
    private String inBankName;

    @Schema(description = "收款账户银行卡号")
    @Size(max = 255, message = "inAccountNo 长度不能超过 255")
    private String inAccountNo;

    @Schema(description = "客户手机号")
    @Size(max = 16, message = "phone 长度不能超过 16")
    private String phone;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "客户名称")
    @Size(max = 255, message = "username 长度不能超过 255")
    private String username;

    @Schema(description = "账户编号. 个人账户/企业账户为集团商户编号; 商户会员为商户编号")
    private Long accountCode;

    @Schema(description = "充值单号")
    @Size(max = 50, message = "orderId 长度不能超过 50")
    private String orderId;

    @Schema(description = "退款单号")
    @Size(max = 50, message = "orderId 长度不能超过 50")
    private String refundOrderId;

    @Schema(description = "申请人id")
    private Long applierId;

    @Schema(description = "申请人的商户id")
    private Long applierCommId;

    @Schema(description = "附件oss列表,json列表")
    private List<FileItem> attachment;

    private Date createTime;

    private Date updateTime;

    @Schema(description = "回款状态(true-已回款;false-未回款)")
    @JsonInclude(Include.NON_NULL)
    private Boolean paymentPlanStatus;

    @Schema(description = "回款计划")
    @JsonInclude(Include.NON_NULL)
    private List<PaymentPlan> paymentPlans;


    // ========= 合约 =========
    @Schema(description = "流程关联合约")
    @JsonInclude(Include.NON_NULL)
    private List<OaContract> contracts;
    // ========= 合约 =========

    @Schema(description = "企客摘要")
    @JsonInclude(Include.NON_NULL)
    private String corpDigest;

    @Schema(description = "测试旧申请流程兼容", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean debugger;

    @Schema(description = "关联流程ID")
    private String procInstId;
}
