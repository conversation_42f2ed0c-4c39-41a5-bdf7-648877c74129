package com.cdz360.biz.model.cus.balance.type;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

/**
 * BalanceApplicationStatusType
 * 
 * @since 6/25/2021 9:20 AM
 * <AUTHOR>
 */
@Getter
public enum BalanceApplicationStatusType implements DcEnum {
    NEW(0, "待初审"),
    RECHECK(1, "待复审"),
    CHECK_FAILED(2, "审核失败"),
    FINISH(3, "已完成"),
    REJECT(4, "复核失败"),
    PASSED(5, "已复核"),

    ;

    private final int code;
    private final String desc;

    BalanceApplicationStatusType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BalanceApplicationStatusType valueOf(int code) {
        for (BalanceApplicationStatusType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return BalanceApplicationStatusType.NEW;
    }
}