package com.cdz360.biz.model.cus.message.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class UserMessageVo extends MessageVo{
    /**
     *是否已读   1-未读 2-已读
     */
    private Long readStatus;

    /**
     * 所属企业ID
     */
    private Long corpId;

}
