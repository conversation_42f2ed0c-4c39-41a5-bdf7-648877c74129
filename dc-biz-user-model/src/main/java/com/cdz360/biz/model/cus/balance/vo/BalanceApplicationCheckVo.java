package com.cdz360.biz.model.cus.balance.vo;

import com.cdz360.biz.model.cus.balance.po.BalanceApplicationCheckPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * BalanceApplicationCheckVo
 *
 * @since 7/5/2021 6:36 PM
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
@Schema(description = "账户金额变更申请-审核")
public class BalanceApplicationCheckVo extends BalanceApplicationCheckPo {
    private String operatorName;
}