package com.cdz360.biz.model.cus.discount.param;

import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "变更场站所属商户删除参数")
public class ChangeSiteCommIdRmParam {
    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "需要移除的场站列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<CorpSimpleVo> removeList;
}
