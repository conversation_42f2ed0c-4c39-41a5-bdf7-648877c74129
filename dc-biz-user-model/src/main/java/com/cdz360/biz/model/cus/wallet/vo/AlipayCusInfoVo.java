package com.cdz360.biz.model.cus.wallet.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 参考文档  https://docs.open.alipay.com/api_2/alipay.user.info.share
 */
@Data
@Accessors(chain = true)
@Schema(description = "支付宝用户信息")
public class AlipayCusInfoVo {

    @Schema(description = "支付宝用户的userId")
    private String alipayCusId;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "用户昵称")
    private String nickName;

    @Schema(description = "是否通过实名认证。T是通过 F是没有实名认证")
    private String isCertified;

    @Schema(description = "性别（F：女性；M：男性）. 只有is_certified为T的时候才有意义，否则不保证准确性.")
    private String gender;
}
