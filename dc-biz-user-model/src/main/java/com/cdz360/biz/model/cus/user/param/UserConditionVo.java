package com.cdz360.biz.model.cus.user.param;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

@Data
public class UserConditionVo implements Serializable {
    /**
     * 序列号
     */
    private static final long serialVersionUID = -2932586069398529730L;
    /**
     * 验证码
     */
    private String code;

    /**
     * 定位当前城市编码
     */
    private String cityCode;

    /**
     * 手机号
     */
    private String phoneNum;
    /**
     * 国家区号
     */
    private String nationalCode;
    /**
     * 验证码类型
     */
    private Integer type;

    /**
     * 头像url 地址 微信小程序/支付宝小程序需传该字段
     */
    private String avatarUrl;

    /**
     * 0未知 1.男,2.女
     */
    private Integer sex;

    /**
     * 昵称 微信小程序/支付宝小程序需传该字段
     */
    private String nickname;

    /**
     * 废弃
     * 客户端类型
     * ‘ios’ iOS客户端
     * ‘android’ Android客户端
     * ‘wechat’ 微信小程序
     * ‘alipay’ 支付宝小程序
     */
    //private String clientType;
    private AppClientType appType;

    //应用来源(1.APP,2.微信 3.平台 4.支付宝,6.绑定卡片时注册)
    private Integer sourceId;

    /**
     * 支付宝授权后的用户code
     */
    private String alipayCode;

    /**
     * 支付宝用户Id
     */
    private String aliUserId;

    /**
     * 支付宝access_token
     */
    private String aliAccessToken;

    /**
     * 是否登录
     */
    private String isLogin;

    /**
     * 商户ID
     */
    private Long commId;

    /**
     * 是否可信，true 不做短信验证码校验
     */
    private Boolean trust;
    /**
     * 其它参数;
     */
    private String scopUserId;
    /**
     * 第三方appId
     */
    private String scopAppId;
    /**
     * 其它参数;
     */
    private String extra;

    /**
     * 第三方应用 appId
     */
    private String appId;

    /**
     * 微信openid
     */
    private String openId;

    private String unionId;

    /**
     * 授权登录传code
     * 微信小程序授权登录，微信公众号授权登录
     */
    private String userCode;
    /**
     * 存到 t_app_device_info 表
     */
    @Schema(description = "app推送的deviceToken")
    private String appPushToken;

    @Schema(description = "微信登录")
    private String sessionKey;

    @Schema(description = "微信登录")
    private String encryptedData;

    @Schema(description = "微信登录")
    private String iv;

    @Schema(description = "支付宝一键登录")
    private String alipayResponse;

    @Schema(description = "授权码 目前高德使用授权码")
    private String authCode;

    @Schema(description = "引流人ID")
    private Long referrerId;

    @Schema(description = "引流人名称")
    private String referrerName;

    @Schema(description = "充电h5登录sign")
    private String authSign;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
