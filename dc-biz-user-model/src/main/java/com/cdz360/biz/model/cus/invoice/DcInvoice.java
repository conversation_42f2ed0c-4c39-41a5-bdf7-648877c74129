package com.cdz360.biz.model.cus.invoice;

import com.cdz360.biz.model.cus.user.po.UserPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since Created on 11:16 2019/3/6.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DcInvoice extends UserPo {


    /**
     * 发票主键ID
     */
    private Integer dcInvoiceId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 开票起始金额（单位分）
     */
    private long invoicedAmount;

    /**
     * 每月几号
     */
    private Integer monthDay;

    /**
     * 是否自动开票（1：是 0:否）
     */
    private Integer auto;


}
