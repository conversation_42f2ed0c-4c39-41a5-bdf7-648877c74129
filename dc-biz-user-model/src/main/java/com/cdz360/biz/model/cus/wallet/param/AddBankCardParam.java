package com.cdz360.biz.model.cus.wallet.param;

import com.cdz360.biz.model.cus.wallet.vo.BankCardVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "新增银行卡参数")
public class AddBankCardParam extends BankCardVo {

}
