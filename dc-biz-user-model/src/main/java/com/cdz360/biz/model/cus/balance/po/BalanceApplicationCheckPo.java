package com.cdz360.biz.model.cus.balance.po;

import com.cdz360.biz.model.cus.balance.type.BalanceCheckResultType;
import com.cdz360.biz.model.cus.balance.type.BalanceCheckType;
import com.cdz360.biz.model.cus.balance.type.BalanceSourceType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "账户金额变更申请-审核")
public class BalanceApplicationCheckPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Schema(description = "申请id")
	@NotNull(message = "applicationId 不能为 null")
	private Long applicationId;

	@Schema(description = "初审CHECK、复审RECHECK、复审REVIEW")
	private BalanceCheckType type;

	@Schema(description = "通过ALLOW、不通过DENY")
	private BalanceCheckResultType result;

	@Schema(description = "备注")
	@Size(max = 255, message = "remark 长度不能超过 255")
	private String remark;

	@Schema(description = "申请来源 OA流程、财务中心充值申请")
	private BalanceSourceType sourceType;

	private Long operatorId;

	private Date createTime;

	private Date updateTime;


}
