package com.cdz360.biz.model.cus.score.param;

import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * ScoreUpdateParam
 *
 * @since 1/9/2023 2:06 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper=false)
public class ScoreUserAddParam {

    private Long scoreSettingId;
    private Boolean enable;

    private Long topCommId;

    private List<String> cusPhoneList;
}