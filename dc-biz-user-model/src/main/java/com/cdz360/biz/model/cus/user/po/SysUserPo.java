package com.cdz360.biz.model.cus.user.po;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * SysUserPo
 * 
 * @since 7/15/2021 3:42 PM
 * <AUTHOR>
 */
@Data
@Schema(description = "系统用户")
@Accessors(chain = true)
public class SysUserPo {

    @Schema(description = "主键id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @Schema(description = "集团商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long topCommId;

    @Schema(description = "系统用户所属商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @Schema(description = "系统用户企业ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    @Schema(description = "系统用户账号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String username;

    @Schema(description = "系统用户密码")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String password;

    @Schema(description = "--")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String salt;

    @Schema(description = "系统用户名字")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String name;

    @Schema(description = "系统用户电话")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String phone;

    @Schema(description = "系统用户电子邮件")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String email;

    @Schema(description = "系统用户归属平台")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long platform;

    @Schema(description = "--")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long createBy;

    @Schema(description = "系统用户状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer status;


    @Schema(description = "上一次登录时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date lastLoginTime;

    @Schema(description = "系统用户创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTime;

    @Schema(description = "系统用户更新时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date updateTime;

    @Schema(description = "--")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String businessImage;

    @Schema(description = "微信openId")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String wxOpenId;

    @Schema(description = "系统用户昵称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String nickname;


//    private Long orgId;
//    private Long orgLevel;
//    private String orgName;
//
//    private String commIdChain;
//
//    /**
//     * 企业相关信息
//     */
//    private CorpPo corpPo;
}