package com.cdz360.biz.model.cus.discount.po;

import com.cdz360.base.model.base.type.PayAccountType;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "预付费协议价配置主键")
public class DiscountPricePrimaryKey {

    @Schema(description = "绑定账号ID(t_user.id)", required = true)
    @NotNull(message = "uid 不能为 null")
    private Long uid;

    @Schema(description = "帐户类型: 0(未知); 1(个人帐户); 2(授信帐户); 3(商户会员); 4(即充即退); 5(企业客户)", required = true)
    @NotNull(message = "accountType 不能为 null")
    private PayAccountType accountType;

    @Schema(description = "账户编号: 商户会员时为所属商户的商户ID，其他账户时为集团商户ID", required = true)
    @NotNull(message = "accountCode 不能为 null")
    private Long accountCode;

    @Schema(description = "站点编号", required = true)
    @NotNull(message = "siteId 不能为 null")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    private String siteId;
}
