package com.cdz360.biz.model.cus.score.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * SearchScoreLogParam
 *
 * @since 1/5/2023 4:58 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SearchScoreLogParam extends BaseListParam {
    private Long userId;
    private Long scoreSettingId;
    private TimeFilter timeFilter;
    private List<String> gids;
}