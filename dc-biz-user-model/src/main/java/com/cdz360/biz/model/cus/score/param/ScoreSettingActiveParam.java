package com.cdz360.biz.model.cus.score.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ScoreSettingActiveParam
 *
 * @since 1/16/2023 4:14 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ScoreSettingActiveParam {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}